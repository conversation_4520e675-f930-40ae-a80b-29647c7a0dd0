# 迁移脚本使用说明

## 概述

`ComprehensiveMigrationV3_EN.s.sol` 脚本已经修改为支持使用 Foundry 的 mock 功能来获取管理员权限。这允许你在链上当前管理员账户不是你的地址时，仍然能够执行需要管理员权限的操作。

## 工作原理

1. **Mock 管理员账户**: 脚本使用 `vm.prank()` 来模拟当前链上的管理员账户
2. **授予权限**: 使用模拟的管理员账户调用 `addAdmin()` 函数，将管理员权限授予你的部署者账户
3. **继续执行**: 一旦获得管理员权限，脚本继续执行原有的迁移逻辑

## 配置步骤

### 1. 设置环境变量

复制 `.env-migration-example` 文件为 `.env` 并填入实际值：

```bash
cp .env-migration-example .env
```

编辑 `.env` 文件：

```bash
# 你的部署者账户私钥
NETWORK_ADMIN_PRIVATE_KEY=0x...

# 合约地址
FACTORY_PROXY_ADDRESS=0x...
POOL_PROXY_ADDRESS=0x...

# 当前链上的管理员地址（将被模拟）
CURRENT_ADMIN_ADDRESS=0x...

# 可选：需要迁移的代币地址
COIN_TO_MIGRATE_1=0x...
```

### 2. 获取当前管理员地址

你可以通过以下方式获取当前管理员地址：

```bash
# 使用 cast 查询合约
cast call <POOL_PROXY_ADDRESS> "checkAdmin(address)" <ADDRESS> --rpc-url <RPC_URL>

# 或者查看合约的 RoleGranted 事件
cast logs --from-block <DEPLOY_BLOCK> --to-block latest \
  --address <POOL_PROXY_ADDRESS> \
  --topic0 0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d \
  --rpc-url <RPC_URL>
```

## 执行脚本

### 在本地测试（推荐）

```bash
# 使用 fork 模式在本地测试
forge script script/ComprehensiveMigrationV3_EN.s.sol:ComprehensiveMigrationV3_EN \
  --fork-url <RPC_URL> \
  --private-key $NETWORK_ADMIN_PRIVATE_KEY \
  -vvvv
```

### 在实际网络上执行

```bash
# 在实际网络上广播交易
forge script script/ComprehensiveMigrationV3_EN.s.sol:ComprehensiveMigrationV3_EN \
  --rpc-url <RPC_URL> \
  --private-key $NETWORK_ADMIN_PRIVATE_KEY \
  --broadcast \
  --verify \
  -vvvv
```

## 脚本执行步骤

脚本将按以下顺序执行：

1. **Step 0**: 使用 mock 管理员账户授予部署者管理员权限
2. **Step 1**: 升级 Factory 合约
3. **Step 2**: 升级 DistributionPool 合约
4. **Step 3**: 执行全局迁移 (`migrateNewVersion()`)
5. **Step 4**: 迁移现有代币到 USDT (`migrateCoinToUSDT()`)

## 安全注意事项

1. **私钥安全**: 确保你的私钥安全，不要在公共环境中暴露
2. **测试优先**: 始终先在 fork 环境中测试脚本
3. **权限验证**: 脚本会验证当前管理员地址确实具有管理员权限
4. **状态检查**: 脚本会检查每个步骤的执行状态并提供详细日志

## 故障排除

### 常见错误

1. **"Current admin address does not have admin rights"**
   - 检查 `CURRENT_ADMIN_ADDRESS` 是否正确
   - 确认该地址确实具有管理员权限

2. **"Failed to verify admin rights grant"**
   - 可能是网络问题或合约状态问题
   - 检查交易是否成功执行

3. **"Deployer already has admin rights"**
   - 这不是错误，脚本会跳过权限授予步骤

### 调试技巧

- 使用 `-vvvv` 参数获取详细日志
- 在 fork 模式下测试以避免实际网络费用
- 检查每个步骤的状态输出

## 示例输出

```
=== Migration Script Configuration ===
Factory Proxy Address: 0x...
Pool Proxy Address: 0x...
Current Admin Address: 0x...
Deployer Address: 0x...

=== Starting Comprehensive Migration Process ===

=== Step 0: Granting Admin Rights to Deployer ===
Using mock admin to grant rights: 0x...
Granting admin rights to: 0x...
Successfully granted admin rights to deployer
Admin rights verification passed

=== Step 1: Upgrading Factory Contract ===
...

=== Migration Process Completed ===
Admin rights grant status: Success
Factory upgrade status: Success
Pool upgrade status: Success
Global migration status: Success
```