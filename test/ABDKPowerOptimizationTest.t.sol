// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {Test, console} from "forge-std/Test.sol";
import {ABDKMath64x64} from "../lib/abdk-libraries-solidity/ABDKMath64x64.sol";
import {FixedPointMathLib} from "@solady/utils/FixedPointMathLib.sol";

/**
 * @title ABDKPowerOptimizationTest
 * @notice Test suite to verify ABDKMath64x64 power function optimization
 * @dev Tests gas efficiency and accuracy of the new _power implementation
 */
contract ABDKPowerOptimizationTest is Test {
    function setUp() public {
        // Simple setup for testing power functions
    }

    /**
     * @notice Test gas consumption for different exponent values using ABDKMath64x64
     * @dev Compares gas usage between small and large exponents
     */
    function testABDKGasEfficiency() public view {
        uint256 base = 950_000; // 0.95 with 6 decimal precision
        uint256 precision = 1_000_000; // 6 decimal precision

        // Test small exponent
        uint256 gasStart = gasleft();
        uint256 result1 = abdkPower(base, precision, 1);
        uint256 gasUsed1 = gasStart - gasleft();

        // Test medium exponent
        gasStart = gasleft();
        uint256 result52 = abdkPower(base, precision, 52);
        uint256 gasUsed52 = gasStart - gasleft();

        // Test large exponent
        gasStart = gasleft();
        uint256 result260 = abdkPower(base, precision, 260);
        uint256 gasUsed260 = gasStart - gasleft();

        console.log("ABDK Gas used for exponent 1: %d", gasUsed1);
        console.log("ABDK Gas used for exponent 52: %d", gasUsed52);
        console.log("ABDK Gas used for exponent 260: %d", gasUsed260);

        console.log("Result for 0.95^1: %d", result1);
        console.log("Result for 0.95^52: %d", result52);
        console.log("Result for 0.95^260: %d", result260);
    }

    /**
     * @notice Test gas consumption for different exponent values using FixedPointMathLib rpow
     * @dev Compares gas usage between small and large exponents for rpow method
     */
    function testRpowGasEfficiency() public view {
        uint256 base = 950_000; // 0.95 with 6 decimal precision
        uint256 precision = 1_000_000; // 6 decimal precision

        // Test small exponent
        uint256 gasStart = gasleft();
        uint256 result1 = rpowPower(base, precision, 1);
        uint256 gasUsed1 = gasStart - gasleft();

        // Test medium exponent
        gasStart = gasleft();
        uint256 result52 = rpowPower(base, precision, 52);
        uint256 gasUsed52 = gasStart - gasleft();

        // Test large exponent
        gasStart = gasleft();
        uint256 result260 = rpowPower(base, precision, 260);
        uint256 gasUsed260 = gasStart - gasleft();

        console.log("rpow Gas used for exponent 1: %d", gasUsed1);
        console.log("rpow Gas used for exponent 52: %d", gasUsed52);
        console.log("rpow Gas used for exponent 260: %d", gasUsed260);

        console.log("Result for 0.95^1: %d", result1);
        console.log("Result for 0.95^52: %d", result52);
        console.log("Result for 0.95^260: %d", result260);
    }

    /**
     * @notice ABDKMath64x64 power function implementation
     * @dev Optimized power calculation using fixed-point arithmetic
     */
    function abdkPower(uint256 base, uint256 precision, uint256 exponent) internal pure returns (uint256 result) {
        if (exponent == 0) {
            return precision; // base^0 = 1 (with precision)
        }

        // Convert to ABDKMath64x64 fixed-point format
        int128 fixedBase = ABDKMath64x64.divu(base, precision);

        // Calculate base^exponent using ABDKMath64x64's efficient pow function
        int128 fixedResult = ABDKMath64x64.pow(fixedBase, exponent);

        // Convert back to uint256 with precision
        result = ABDKMath64x64.mulu(fixedResult, precision);

        return result;
    }

    /**
     * @notice FixedPointMathLib rpow function implementation
     * @dev Uses Solady's rpow function for power calculation
     */
    function rpowPower(uint256 base, uint256 precision, uint256 exponent) internal pure returns (uint256 result) {
        if (exponent == 0) {
            return precision; // base^0 = 1 (with precision)
        }

        // Use FixedPointMathLib's rpow function: rpow(x, y, b) calculates x^y denominated in base b
        result = FixedPointMathLib.rpow(base, exponent, precision);

        return result;
    }

    /**
     * @notice Traditional iterative power function for comparison
     * @dev Uses binary exponentiation but still scales with exponent size
     */
    function traditionalPower(
        uint256 base,
        uint256 precision,
        uint256 exponent
    ) internal pure returns (uint256 result) {
        if (exponent == 0) {
            return precision;
        }

        result = precision;
        uint256 currentBase = base;
        uint256 currentExponent = exponent;

        while (currentExponent > 0) {
            if (currentExponent % 2 == 1) {
                result = (result * currentBase) / precision;
            }
            currentBase = (currentBase * currentBase) / precision;
            currentExponent = currentExponent / 2;
        }

        return result;
    }

    /**
     * @notice Test accuracy of power calculation
     * @dev Verifies that ABDKMath64x64 maintains precision
     */
    function testPowerAccuracy() public view {
        uint256 base = 950_000; // 0.95
        uint256 precision = 1_000_000;

        // Test basic cases
        uint256 result0 = abdkPower(base, precision, 0);
        uint256 result1 = abdkPower(base, precision, 1);
        uint256 result2 = abdkPower(base, precision, 2);
        uint256 result3 = abdkPower(base, precision, 3);

        console.log("ABDK 0.95^0 = %d (expected: %d)", result0, precision);
        console.log("ABDK 0.95^1 = %d (expected: %d)", result1, base);
        console.log("ABDK 0.95^2 = %d", result2);
        console.log("ABDK 0.95^3 = %d", result3);

        // Verify decreasing pattern
        assertTrue(result1 > result2, "0.95^1 > 0.95^2");
        assertTrue(result2 > result3, "0.95^2 > 0.95^3");
    }

    /**
     * @notice Performance comparison between ABDK, rpow and traditional methods
     * @dev Demonstrates gas efficiency improvements across all implementations
     */
    function testPerformanceComparison() public view {
        uint256 base = 950_000; // 0.95
        uint256 precision = 1_000_000;

        console.log("=== Performance Comparison: ABDK vs rpow vs Traditional ===");

        uint256[] memory exponents = new uint256[](4);
        exponents[0] = 10;
        exponents[1] = 52;
        exponents[2] = 104;
        exponents[3] = 260;

        for (uint256 i = 0; i < exponents.length; i++) {
            uint256 exp = exponents[i];

            // Test ABDK method
            uint256 gasStart = gasleft();
            uint256 abdkResult = abdkPower(base, precision, exp);
            uint256 abdkGas = gasStart - gasleft();

            // Test rpow method
            gasStart = gasleft();
            uint256 rpowResult = rpowPower(base, precision, exp);
            uint256 rpowGas = gasStart - gasleft();

            // Test traditional method
            gasStart = gasleft();
            uint256 tradResult = traditionalPower(base, precision, exp);
            uint256 tradGas = gasStart - gasleft();

            console.log("Exponent %d:", exp);
            console.log("  ABDK Gas: %d, Result: %d", abdkGas, abdkResult);
            console.log("  rpow Gas: %d, Result: %d", rpowGas, rpowResult);
            console.log("  Traditional Gas: %d, Result: %d", tradGas, tradResult);

            if (tradGas > 0) {
                uint256 abdkImprovement = ((tradGas - abdkGas) * 100) / tradGas;
                uint256 rpowImprovement = ((tradGas - rpowGas) * 100) / tradGas;
                console.log("  ABDK vs Traditional improvement: %d%%", abdkImprovement);
                console.log("  rpow vs Traditional improvement: %d%%", rpowImprovement);
            }
            
            if (abdkGas > 0 && rpowGas > 0) {
                if (rpowGas < abdkGas) {
                    uint256 rpowVsAbdk = ((abdkGas - rpowGas) * 100) / abdkGas;
                    console.log("  rpow vs ABDK improvement: %d%%", rpowVsAbdk);
                } else {
                    uint256 abdkVsRpow = ((rpowGas - abdkGas) * 100) / rpowGas;
                    console.log("  ABDK vs rpow improvement: %d%%", abdkVsRpow);
                }
            }
            console.log("");
        }
    }

    /**
     * @notice Compare ABDK vs rpow vs Traditional implementations with identical inputs
     * @dev Ensures no precision errors were introduced in any optimization
     */
    function testImplementationConsistency() public view {
        uint256 base = 950000; // 0.95
        uint256 precision = 1000000;
        
        console.log("=== Implementation Consistency Test ===");
        console.log("Testing identical inputs with all three implementations");
        console.log("Base: 0.95, Precision: 1,000,000");
        console.log("");
        
        // Test various exponents
        uint256[] memory testExponents = new uint256[](8);
        testExponents[0] = 0;
        testExponents[1] = 1;
        testExponents[2] = 2;
        testExponents[3] = 10;
        testExponents[4] = 25;
        testExponents[5] = 52;
        testExponents[6] = 104;
        testExponents[7] = 260;
        
        for (uint256 i = 0; i < testExponents.length; i++) {
            uint256 exp = testExponents[i];
            
            uint256 abdkResult = abdkPower(base, precision, exp);
            uint256 rpowResult = rpowPower(base, precision, exp);
            uint256 tradResult = traditionalPower(base, precision, exp);
            
            console.log("Exponent %d:", exp);
            console.log("  ABDK Result:        %d", abdkResult);
            console.log("  rpow Result:        %d", rpowResult);
            console.log("  Traditional Result: %d", tradResult);
            
            // Calculate differences
            uint256 abdkTradDiff = abdkResult > tradResult ? abdkResult - tradResult : tradResult - abdkResult;
            uint256 rpowTradDiff = rpowResult > tradResult ? rpowResult - tradResult : tradResult - rpowResult;
            uint256 abdkRpowDiff = abdkResult > rpowResult ? abdkResult - rpowResult : rpowResult - abdkResult;
            
            console.log("  ABDK vs Trad Diff:  %d", abdkTradDiff);
            console.log("  rpow vs Trad Diff:  %d", rpowTradDiff);
            console.log("  ABDK vs rpow Diff:  %d", abdkRpowDiff);
            
            // Calculate relative error percentages (scaled by 10000 for precision)
            if (tradResult > 0) {
                uint256 abdkRelativeError = (abdkTradDiff * 10000) / tradResult;
                uint256 rpowRelativeError = (rpowTradDiff * 10000) / tradResult;
                console.log("  ABDK Relative Error: %d.%d%%", abdkRelativeError / 100, (abdkRelativeError % 100) / 10);
                console.log("  rpow Relative Error: %d.%d%%", rpowRelativeError / 100, (rpowRelativeError % 100) / 10);
            }
            
            if (abdkResult > 0) {
                uint256 rpowVsAbdkError = (abdkRpowDiff * 10000) / abdkResult;
                console.log("  rpow vs ABDK Error:  %d.%d%%", rpowVsAbdkError / 100, (rpowVsAbdkError % 100) / 10);
            }
            
            console.log("");
            
            // Assert results are very close (allow small precision differences)
            // For most cases, difference should be minimal
            if (exp > 0 && tradResult > 100) {
                // Allow up to 0.1% difference for large calculations
                uint256 maxAllowedDiff = tradResult / 1000;
                assertTrue(abdkTradDiff <= maxAllowedDiff, "ABDK and Traditional results should be very close");
                assertTrue(rpowTradDiff <= maxAllowedDiff, "rpow and Traditional results should be very close");
            }
        }
    }
    
    /**
     * @notice Test edge cases
     * @dev Tests boundary conditions and edge cases
     */
    function testEdgeCases() public view {
        uint256 precision = 1000000;
        
        console.log("=== Edge Cases Test ===");
        
        // Test with base = 1.0 (no decay)
        uint256 result = abdkPower(precision, precision, 100);
        console.log("1.0^100 = %d (expected: %d)", result, precision);
        
        // Test with very small base
        uint256 smallBase = 1; // 0.000001
        uint256 smallResult = abdkPower(smallBase, precision, 2);
        console.log("0.000001^2 = %d", smallResult);
        assertTrue(smallResult < smallBase, "Very small base should decay further");
        
        // Test large exponents
        uint256 largeExpResult = abdkPower(950000, precision, 1000);
        console.log("0.95^1000 = %d", largeExpResult);
        
        // Test different precision scales
        uint256 result18 = abdkPower(950000000000000000, 1000000000000000000, 52); // 18 decimals
        console.log("0.95^52 with 18 decimals = %d", result18);
    }
}
