# Migration Script Environment Variables Example
# Copy this file to .env and fill in the actual values

# Network admin private key (your deployer account)
NETWORK_ADMIN_PRIVATE_KEY=0x...

# Contract addresses
FACTORY_PROXY_ADDRESS=0x...
POOL_PROXY_ADDRESS=0x...

# Current admin address on chain (the account that currently has admin rights)
# This will be used to mock and grant admin rights to your deployer account
CURRENT_ADMIN_ADDRESS=0x...

# Optional: Coins to migrate (up to 10)
COIN_TO_MIGRATE_1=0x...
COIN_TO_MIGRATE_2=0x...
# COIN_TO_MIGRATE_3=0x...
# ... up to COIN_TO_MIGRATE_10