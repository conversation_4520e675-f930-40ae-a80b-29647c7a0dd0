# Mock Admin Testing Guide

这个指南说明如何使用 `ComprehensiveMigrationV3_EN.s.sol` 脚本的 mock 管理员功能来安全地测试管理员操作。

## 功能概述

Mock 管理员模式允许你：
- 使用 Foundry 的 `vm.startPrank()` 功能模拟真实管理员账户
- 在不需要真实私钥的情况下测试管理员操作
- 安全地测试合约升级和迁移流程
- 使用 fork 模式在真实网络状态下进行测试

## 配置步骤

### 1. 设置环境变量

复制示例配置文件：
```bash
cp .env.mock-admin-example .env
```

编辑 `.env` 文件，设置以下变量：

```bash
# 启用 mock 管理员模式
USE_MOCK_ADMIN=true

# 真实管理员地址（可选，脚本会尝试从合约获取）
REAL_ADMIN_ADDRESS=******************************************

# 合约地址（必需）
FACTORY_PROXY_ADDRESS=0xYourFactoryProxyAddress
POOL_PROXY_ADDRESS=0xYourPoolProxyAddress

# 备用私钥（任何有效私钥，mock 模式下不会使用）
NETWORK_ADMIN_PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80

# 网络 RPC URL
RPC_URL=https://your-rpc-endpoint.com
```

### 2. 测试管理员权限

在执行实际迁移之前，先测试管理员权限：

```bash
forge script script/ComprehensiveMigrationV3_EN.s.sol:ComprehensiveMigrationV3_EN \
  --sig "testAdminPermissions()" \
  --fork-url $RPC_URL \
  -vvv
```

这个命令会：
- 检查 Factory 和 DistributionPool 的所有者
- 验证当前模拟的管理员是否有权限
- 测试升级访问权限

### 3. 运行迁移脚本

如果权限测试通过，运行完整的迁移：

```bash
forge script script/ComprehensiveMigrationV3_EN.s.sol:ComprehensiveMigrationV3_EN \
  --fork-url $RPC_URL \
  -vvv
```

### 4. 查看使用说明

获取详细的使用说明：

```bash
forge script script/ComprehensiveMigrationV3_EN.s.sol:ComprehensiveMigrationV3_EN \
  --sig "printUsageInstructions()" \
  -vvv
```

## 工作原理

### Mock 管理员模式

当 `USE_MOCK_ADMIN=true` 时：

1. **地址获取**：脚本尝试获取真实管理员地址：
   - 首先从 `REAL_ADMIN_ADDRESS` 环境变量
   - 然后从合约的 `owner()` 函数
   - 最后回退到部署者地址

2. **权限模拟**：使用 `vm.startPrank(realAdminAddress)` 模拟管理员身份

3. **资金分配**：给 mock 地址分配 ETH 用于 gas 费用

4. **安全执行**：所有操作在 fork 环境中执行，不会影响真实网络

### 正常模式

当 `USE_MOCK_ADMIN=false` 或未设置时：
- 使用传统的 `vm.startBroadcast(deployerPrivateKey)` 方式
- 需要真实的管理员私钥
- 可以发送真实交易到网络

## 安全注意事项

1. **Fork 模式**：始终使用 `--fork-url` 参数，确保在 fork 环境中测试
2. **私钥安全**：Mock 模式下不需要真实管理员私钥
3. **权限验证**：运行迁移前先执行权限测试
4. **网络确认**：确保连接到正确的网络 RPC

## 故障排除

### 常见问题

1. **"Cannot access Factory owner information"**
   - 检查 `FACTORY_PROXY_ADDRESS` 是否正确
   - 确保合约已部署且可访问

2. **"Factory admin access: DENIED"**
   - 检查 `REAL_ADMIN_ADDRESS` 是否为合约的真实所有者
   - 验证合约的所有权结构

3. **"Factory upgrade access: DENIED or ERROR"**
   - 确保管理员地址有升级权限
   - 检查合约是否支持 UUPS 升级模式

### 调试技巧

1. 使用 `-vvv` 参数获取详细日志
2. 检查环境变量是否正确设置：`echo $USE_MOCK_ADMIN`
3. 验证 RPC 连接：`curl -X POST -H "Content-Type: application/json" --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' $RPC_URL`

## 示例输出

成功的权限测试输出：
```
=== Testing Admin Permissions ===
Factory owner: ******************************************
Current caller: ******************************************
Factory admin access: GRANTED
Pool owner: ******************************************
Pool admin access: GRANTED

--- Testing Upgrade Capability ---
Factory upgrade access: LIKELY GRANTED (no revert on proxy access)

=== Admin Permission Test Completed ===
```

## 总结

Mock 管理员功能提供了一个安全、便捷的方式来测试管理员操作，无需暴露真实的私钥。通过 Foundry 的 fork 和 prank 功能，你可以在真实的网络状态下测试复杂的合约交互，确保迁移脚本在实际执行前能够正常工作。