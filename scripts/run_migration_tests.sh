#!/bin/bash

# Migration Testing Script for DistributionPool
# This script runs comprehensive tests for the migration functionality

set -e

echo "🚀 Starting DistributionPool Migration Tests"
echo "============================================="

# Configuration
FORK_URL="https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd"
DISTRIBUTION_POOL_ADDRESS="" # TODO: Update with actual deployed address

# Check if required environment variables are set
if [ -z "$DISTRIBUTION_POOL_ADDRESS" ]; then
    echo "⚠️  Warning: DISTRIBUTION_POOL_ADDRESS not set in the script"
    echo "   Please update the script with the actual deployed contract address"
fi

# Set proxy if needed (uncomment if required)
# export https_proxy=http://127.0.0.1:7897
# export http_proxy=http://127.0.0.1:7897
# export all_proxy=socks5://127.0.0.1:7897

echo "📋 Test Configuration:"
echo "   Fork URL: $FORK_URL"
echo "   Distribution Pool: $DISTRIBUTION_POOL_ADDRESS"
echo ""

# Function to run a test with proper error handling
run_test() {
    local test_name=$1
    local test_command=$2

    echo "🧪 Running: $test_name"
    echo "   Command: $test_command"

    if eval $test_command; then
        echo "✅ $test_name - PASSED"
    else
        echo "❌ $test_name - FAILED"
        return 1
    fi
    echo ""
}

# Test 1: Basic Migration Test
echo "=== Phase 1: Basic Migration Tests ==="
run_test "Migration New Version Test" \
    "forge test --match-test testMigrateNewVersion --fork-url $FORK_URL -vvv"

run_test "Migration Access Control Test" \
    "forge test --match-test testMigrationAccessControl --fork-url $FORK_URL -vvv"

run_test "Migration Idempotency Test" \
    "forge test --match-test testMigrationIdempotency --fork-url $FORK_URL -vvv"

# Test 2: Parameter Update Tests
echo "=== Phase 2: Parameter Update Tests ==="
run_test "Parameter Updates Test" \
    "forge test --match-test testParameterUpdates --fork-url $FORK_URL -vvv"

# Test 3: Swap Functionality Tests
echo "=== Phase 3: Swap Functionality Tests ==="
run_test "Swap Functionality Test" \
    "forge test --match-test testSwapFunctionality --fork-url $FORK_URL -vvv"

# Test 4: Coin Migration Tests
echo "=== Phase 4: Coin Migration Tests ==="
run_test "Coin Migration Test" \
    "forge test --match-test testMigrateCoinToUSDT --fork-url $FORK_URL -vvv"

# Test 5: Full Test Suite
echo "=== Phase 5: Full Test Suite ==="
run_test "Complete Migration Test Suite" \
    "forge test --match-contract MigrationTest --fork-url $FORK_URL -vvv"

echo "🎉 All Migration Tests Completed!"
echo ""

# Optional: Run migration simulation
echo "=== Optional: Migration Simulation ==="
echo "To run simple migration test (check current state), execute:"
echo "forge script script/SimpleMigrationTest.s.sol --fork-url $FORK_URL -vvv"
echo ""
echo "To run migration simulation (dry-run), execute:"
echo "forge script script/SimpleMigrationTest.s.sol:SimpleMigrationTest --sig 'simulateMigration()' --fork-url $FORK_URL -vvv"
echo ""

echo "⚠️  IMPORTANT REMINDERS:"
echo "1. Update DISTRIBUTION_POOL_ADDRESS in test files before running"
echo "2. Ensure sufficient test ETH in the contract for swap testing"
echo "3. Verify USDT token address is correct for your network"
echo "4. Test on fork before executing on mainnet"
echo "5. Have emergency procedures ready"
echo ""

echo "📚 Next Steps:"
echo "1. Review test results carefully"
echo "2. If all tests pass, proceed with simulation"
echo "3. If simulation succeeds, prepare for mainnet execution"
echo "4. Execute migration with proper monitoring"
echo "5. Run post-migration verification"
