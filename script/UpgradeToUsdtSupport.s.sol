// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {CreatorNft} from "../src/CreatorNft.sol";
import {TraderNft} from "../src/TraderNft.sol";

/**
 * @title UpgradeToUsdtSupport
 * @notice Comprehensive upgrade script to enable USDT support across Factory and DistributionPool contracts
 * @dev This script performs the following operations in sequence:
 *      1. Deploy new logic contracts (CreatorNft, TraderNft)
 *      2. Upgrade Factory contract
 *      3. Update Factory's logic implementations
 *      4. Initialize Factory's USDT support
 *      5. Upgrade DistributionPool contract
 *      6. Set USDT token address in DistributionPool
 *      7. Enable new version parameters in DistributionPool
 */
contract UpgradeToUsdtSupport is Script {
    // Environment variables
    address factoryProxy;
    address distributionPoolProxy;
    address usdtToken;
    uint256 deployerPrivate<PERSON>ey;

    // New logic contract addresses
    address newCreatorNftLogic;
    address newTraderNftLogic;

    function setUp() public {
        // Read addresses from environment variables
        factoryProxy = vm.envAddress("FACTORY_PROXY_ADDRESS");
        distributionPoolProxy = vm.envAddress("POOL_PROXY_ADDRESS");
        usdtToken = vm.envAddress("USDT_ADDRESS");
        deployerPrivateKey = vm.envUint("DEPLOYER_KEY");

        console.log("=== Environment Variables ===");
        console.log("Factory Proxy:", factoryProxy);
        console.log("DistributionPool Proxy:", distributionPoolProxy);
        console.log("USDT Token:", usdtToken);
    }

    function run() public {
        vm.startBroadcast(deployerPrivateKey);

        console.log("=== Starting USDT Support Upgrade Process ===");

        // Step 1: Deploy new logic contracts
        deployNewLogicContracts();

        // Step 2: Upgrade Factory contract
        upgradeFactoryContract();

        // Step 3: Update Factory's logic implementations
        updateFactoryImplementations();

        // Step 4: Initialize Factory's USDT support
        initializeFactoryUsdtSupport();

        // Step 5: Upgrade DistributionPool contract
        upgradeDistributionPoolContract();

        // Step 6: Set USDT token address in DistributionPool
        setDistributionPoolUsdtToken();

        // Step 7: Enable new version parameters in DistributionPool
        enableNewVersionParameters();

        console.log("=== USDT Support Upgrade Completed Successfully ===");

        vm.stopBroadcast();
    }

    /**
     * @notice Step 1: Deploy new logic contracts (CreatorNft, TraderNft)
     */
    function deployNewLogicContracts() internal {
        console.log("=== Step 1: Deploying New Logic Contracts ===");

        // Deploy CreatorNft logic with CREATE2 to avoid collision
        bytes32 creatorSalt = keccak256(abi.encodePacked("CreatorNft_Logic_", block.timestamp));
        CreatorNft creatorNftLogic = new CreatorNft{salt: creatorSalt}();
        newCreatorNftLogic = address(creatorNftLogic);
        console.log("CreatorNft Logic deployed at:", newCreatorNftLogic);

        // Deploy TraderNft logic with CREATE2 to avoid collision
        bytes32 traderSalt = keccak256(abi.encodePacked("TraderNft_Logic_", block.timestamp));
        TraderNft traderNftLogic = new TraderNft{salt: traderSalt}();
        newTraderNftLogic = address(traderNftLogic);
        console.log("TraderNft Logic deployed at:", newTraderNftLogic);
    }

    /**
     * @notice Step 2: Upgrade Factory contract to new implementation
     */
    function upgradeFactoryContract() internal {
        console.log("=== Step 2: Upgrading Factory Contract ===");

        // Deploy new Factory implementation with CREATE2 to avoid collision
        bytes32 factorySalt = keccak256(abi.encodePacked("Factory_Implementation_", block.timestamp));
        Factory newFactoryImplementation = new Factory{salt: factorySalt}();
        console.log("New Factory implementation deployed at:", address(newFactoryImplementation));

        // Upgrade the proxy
        UUPSUpgradeable factoryProxyContract = UUPSUpgradeable(factoryProxy);
        factoryProxyContract.upgradeToAndCall(address(newFactoryImplementation), "");

        console.log("Factory proxy upgraded to new implementation");
    }

    /**
     * @notice Step 3: Update Factory's logic implementations
     */
    function updateFactoryImplementations() internal {
        console.log("=== Step 3: Updating Factory Logic Implementations ===");

        Factory factory = Factory(factoryProxy);

        // Call setImplementations to update TraderNft and CreatorNft logic
        factory.setImplementations(newTraderNftLogic, newCreatorNftLogic);

        console.log("Factory implementations updated:");
        console.log("  TraderNft Logic:", newTraderNftLogic);
        console.log("  CreatorNft Logic:", newCreatorNftLogic);
    }

    /**
     * @notice Step 4: Initialize Factory's USDT support
     */
    function initializeFactoryUsdtSupport() internal {
        console.log("=== Step 4: Initializing Factory USDT Support ===");

        Factory factory = Factory(factoryProxy);

        // Set minimum USDT buy value (e.g., 1 USDT = 1e6 for 6 decimals)
        uint256 minimumUsdtBuyValue = 0;

        // Call initializeUsdtSupport
        factory.initializeUsdtSupport(usdtToken, minimumUsdtBuyValue);

        console.log("Factory USDT support initialized:");
        console.log("  USDT Token:", usdtToken);
        console.log("  Minimum USDT Buy Value:", minimumUsdtBuyValue);
    }

    /**
     * @notice Step 5: Upgrade DistributionPool contract to new implementation
     */
    function upgradeDistributionPoolContract() internal {
        console.log("=== Step 5: Upgrading DistributionPool Contract ===");

        // Deploy new DistributionPool implementation with CREATE2 to avoid collision
        bytes32 poolSalt = keccak256(abi.encodePacked("DistributionPool_Implementation_", block.timestamp));
        DistributionPool newPoolImplementation = new DistributionPool{salt: poolSalt}();
        console.log("New DistributionPool implementation deployed at:", address(newPoolImplementation));

        // Upgrade the proxy
        UUPSUpgradeable poolProxyContract = UUPSUpgradeable(distributionPoolProxy);
        poolProxyContract.upgradeToAndCall(address(newPoolImplementation), "");

        console.log("DistributionPool proxy upgraded to new implementation");
    }

    /**
     * @notice Step 6: Set USDT token address in DistributionPool
     */
    function setDistributionPoolUsdtToken() internal {
        console.log("=== Step 6: Setting DistributionPool USDT Token ===");

        DistributionPool pool = DistributionPool(payable(distributionPoolProxy));

        // Call setUsdtToken to enable USDT support
        pool.setUsdtToken(usdtToken);

        console.log("DistributionPool USDT token set to:", usdtToken);
    }

    /**
     * @notice Step 7: Enable new version parameters in DistributionPool
     */
    function enableNewVersionParameters() internal {
        console.log("=== Step 7: Enabling New Version Parameters ===");

        DistributionPool pool = DistributionPool(payable(distributionPoolProxy));

        // Call migrateNewVersion to enable new parameter style
        pool.migrateNewVersion();

        console.log("New version parameters enabled in DistributionPool");
    }
}
