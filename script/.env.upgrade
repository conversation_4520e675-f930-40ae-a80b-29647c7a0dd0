# Environment variables for UpgradeToUsdtSupport.s.sol
# Copy this file to .env and fill in the actual addresses

# Factory proxy contract address (the deployed proxy, not implementation)
export FACTORY_PROXY_ADDRESS=0xf64a3D8e664dFB8172eaac2792c064C2e146876B

# DistributionPool proxy contract address (the deployed proxy, not implementation)
export POOL_PROXY_ADDRESS=******************************************

# USDT token contract address (the actual USDT token contract)
export USDT_TOKEN_ADDRESS=******************************************
export USDT_ADDRESS=******************************************

export COLLECTOR_PROXY_ADDRESS=******************************************

# Optional: RPC URL for the network (if not using default)
# RPC_URL=https://mainnet.infura.io/v3/your-project-id

# Optional: Etherscan API key for contract verification
# ETHERSCAN_API_KEY=your-etherscan-api-key
