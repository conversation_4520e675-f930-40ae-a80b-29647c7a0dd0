// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";

/**
 * @title TestMigration
 * @notice Test script to verify migration script functionality without executing transactions
 * @dev This script performs read-only operations to check the current state
 */
contract TestMigration is Script {
    Factory private factory;
    DistributionPool private distributionPool;

    function setUp() public {
        address factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        factory = Factory(factoryProxyAddress);
        distributionPool = DistributionPool(payable(poolProxyAddress));
    }

    function run() public view {
        console.log("=== Migration State Check ===");

        // Check Factory state
        checkFactoryState();

        // Check DistributionPool state
        checkDistributionPoolState();

        // Check permissions
        checkPermissions();

        // Check coin migration status (if coins are specified)
        checkCoinMigrationStatus();

        console.log("=== Migration State Check Completed ===");
    }

    function checkFactoryState() internal view {
        console.log("=== Factory State ===");

        try factory.admin() returns (address admin) {
            console.log("Factory admin:", admin);
        } catch {
            console.log("Failed to get Factory admin");
        }

        try factory.distributionPool() returns (DistributionPool pool) {
            console.log("Factory distributionPool:", address(pool));
        } catch {
            console.log("Failed to get Factory distributionPool");
        }

        try factory.minimumUsdtBuyValue() returns (uint256 minBuy) {
            console.log("Factory minimumUsdtBuyValue:", minBuy);
        } catch {
            console.log("Failed to get Factory minimumUsdtBuyValue");
        }
    }

    function checkDistributionPoolState() internal view {
        console.log("=== DistributionPool State ===");

        try distributionPool.currentVersion() returns (uint256 version) {
            console.log("Current version:", version);
            if (version >= 2_025_081_901) {
                console.log("Already migrated to new version");
            } else {
                console.log("Not yet migrated to new version");
            }
        } catch {
            console.log("Failed to get current version");
        }

        try distributionPool.totalPlatformFee() returns (uint256 fee) {
            console.log("Total platform fee:", fee);
        } catch {
            console.log("Failed to get total platform fee");
        }

        try distributionPool.bondingCurveTradeCap() returns (uint256 cap) {
            console.log("Bonding curve trade cap:", cap);
            uint256 expectedCap = 400_000_000 * 1e18;
            if (cap == expectedCap) {
                console.log("Bonding curve trade cap is correct");
            } else {
                console.log("Bonding curve trade cap needs update");
            }
        } catch {
            console.log("Failed to get bonding curve trade cap");
        }

        try distributionPool.minimumUsdtToBuy() returns (uint256 minBuy) {
            console.log("Minimum USDT to buy:", minBuy);
            uint256 expectedMinBuy = 1 * 1e3;
            if (minBuy == expectedMinBuy) {
                console.log("Minimum USDT to buy is correct");
            } else {
                console.log("Minimum USDT to buy needs update");
            }
        } catch {
            console.log("Failed to get minimum USDT to buy");
        }

        console.log("Contract native balance:", address(distributionPool).balance);
    }

    function checkPermissions() internal view {
        console.log("=== Permission Check ===");

        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        console.log("Deployer address:", deployer);

        // Check Factory owner
        try factory.owner() returns (address owner) {
            console.log("Factory owner:", owner);
            if (owner == deployer) {
                console.log("Deployer has Factory owner permission");
            } else {
                console.log("Deployer does NOT have Factory owner permission");
            }
        } catch {
            console.log("Failed to get Factory owner");
        }

        // Check DistributionPool admin role
        try distributionPool.ADMIN_ROLE() returns (bytes32 adminRole) {
            bool hasAdminRole = distributionPool.hasRole(adminRole, deployer);
            console.log("Has DistributionPool admin role:", hasAdminRole);
            if (hasAdminRole) {
                console.log("Deployer has DistributionPool admin permission");
            } else {
                console.log("Deployer does NOT have DistributionPool admin permission");
            }
        } catch {
            console.log("Failed to check DistributionPool admin role");
        }
    }

    function checkCoinMigrationStatus() internal view {
        console.log("=== Coin Migration Status ===");

        // Check coins specified in environment variables
        address[] memory coinsToCheck = new address[](0);

        // Try to get coin addresses from environment
        try vm.envAddress("COIN_TO_MIGRATE_1") returns (address coin1) {
            console.log("Checking coin 1:", coin1);
            checkSingleCoinStatus(coin1);
        } catch {
            console.log("No COIN_TO_MIGRATE_1 specified");
        }

        try vm.envAddress("COIN_TO_MIGRATE_2") returns (address coin2) {
            console.log("Checking coin 2:", coin2);
            checkSingleCoinStatus(coin2);
        } catch {
            console.log("No COIN_TO_MIGRATE_2 specified");
        }

        try vm.envAddress("COIN_TO_MIGRATE_3") returns (address coin3) {
            console.log("Checking coin 3:", coin3);
            checkSingleCoinStatus(coin3);
        } catch {
            console.log("No COIN_TO_MIGRATE_3 specified");
        }
    }

    function checkSingleCoinStatus(address coinAddress) internal view {
        console.log("  Coin address:", coinAddress);

        // Check if coin is migrated to USDT
        try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
            console.log("  Migrated to USDT:", migrated);
            if (migrated) {
                console.log("Coin is already migrated");
            } else {
                console.log("Coin needs migration");
            }
        } catch {
            console.log("  Failed to check migration status");
        }

        // Try to get basic coin pool data
        try distributionPool.getCoinPoolBasicData(coinAddress) returns (
            address ipCoinContract,
            address creatorNftContract,
            bool allowTrade
        ) {
            console.log("  IP Coin Contract:", ipCoinContract);
            console.log("  Creator NFT Contract:", creatorNftContract);
            console.log("  Allow Trade:", allowTrade);

            if (ipCoinContract != address(0)) {
                console.log("Coin exists in pool");
            } else {
                console.log("Coin does NOT exist in pool");
            }
        } catch {
            console.log("  Failed to get coin pool data");
        }
    }

    /**
     * @notice Check if migration is needed
     */
    function isMigrationNeeded() external view returns (bool needed, string memory reason) {
        // Check version
        try distributionPool.currentVersion() returns (uint256 version) {
            if (version < 2_025_081_901) {
                return (true, "Version needs update");
            }
        } catch {
            return (true, "Cannot read version");
        }

        // Check parameters
        try distributionPool.bondingCurveTradeCap() returns (uint256 cap) {
            if (cap != 400_000_000 * 1e18) {
                return (true, "Bonding curve trade cap needs update");
            }
        } catch {
            return (true, "Cannot read bonding curve trade cap");
        }

        try distributionPool.minimumUsdtToBuy() returns (uint256 minBuy) {
            if (minBuy != 1 * 1e3) {
                return (true, "Minimum USDT to buy needs update");
            }
        } catch {
            return (true, "Cannot read minimum USDT to buy");
        }

        return (false, "Migration not needed");
    }

    /**
     * @notice Get migration readiness status
     */
    function getMigrationReadiness() external view returns (
        bool hasFactoryPermission,
        bool hasPoolPermission,
        bool sufficientBalance,
        string memory status
    ) {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        // Check Factory permission
        try factory.owner() returns (address owner) {
            hasFactoryPermission = (owner == deployer);
        } catch {
            hasFactoryPermission = false;
        }

        // Check Pool permission
        try distributionPool.ADMIN_ROLE() returns (bytes32 adminRole) {
            hasPoolPermission = distributionPool.hasRole(adminRole, deployer);
        } catch {
            hasPoolPermission = false;
        }

        // Check balance (simple check for non-zero balance)
        sufficientBalance = deployer.balance > 0;

        // Determine status
        if (hasFactoryPermission && hasPoolPermission && sufficientBalance) {
            status = "Ready for migration";
        } else if (!hasFactoryPermission) {
            status = "Missing Factory owner permission";
        } else if (!hasPoolPermission) {
            status = "Missing DistributionPool admin permission";
        } else if (!sufficientBalance) {
            status = "Insufficient balance for gas";
        } else {
            status = "Unknown issue";
        }
    }
}
