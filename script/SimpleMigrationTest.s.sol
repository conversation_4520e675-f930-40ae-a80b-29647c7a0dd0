// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";
import "forge-std/console.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract SimpleMigrationTest is Script {
    DistributionPool public distributionPool;
    IERC20 public usdtToken;

    // Configuration - Update these with actual deployed addresses
    address constant DISTRIBUTION_POOL_ADDRESS = address(0); // UPDATE THIS
    address constant USDT_ADDRESS = ******************************************; // Base Sepolia WETH as USDT

    function run() public view {
        // Initialize contracts
        DistributionPool pool = DistributionPool(payable(DISTRIBUTION_POOL_ADDRESS));
        IERC20 usdt = IERC20(USDT_ADDRESS);

        console.log("=== Simple Migration Test Started ===");
        console.log("DistributionPool Address:", address(pool));
        console.log("USDT Token Address:", address(usdt));

        // Check current state
        _checkCurrentState(pool, usdt);

        console.log("=== Simple Migration Test Completed ===");
    }

    function _checkCurrentState(DistributionPool pool, IERC20 usdt) internal view {
        console.log("\n=== Current Contract State ===");

        // Check version
        uint256 currentVersion = pool.currentVersion();
        console.log("Current version:", currentVersion);

        // Check balances
        uint256 contractNativeBalance = address(pool).balance;
        uint256 contractUsdtBalance = usdt.balanceOf(address(pool));
        uint256 totalPlatformFee = pool.totalPlatformFee();

        console.log("Contract native balance:", contractNativeBalance);
        console.log("Contract USDT balance:", contractUsdtBalance);
        console.log("Total platform fee:", totalPlatformFee);

        // Check migration readiness
        console.log("\n=== Migration Readiness Check ===");

        bool needsMigration = currentVersion < 2_025_081_901;
        console.log("Needs migration:", needsMigration);

        if (needsMigration) {
            bool hasNativeBalance = contractNativeBalance >= totalPlatformFee;
            console.log("Has sufficient native balance:", hasNativeBalance);

            if (totalPlatformFee > 0) {
                console.log("Will convert", totalPlatformFee, "native tokens to USDT");
            } else {
                console.log("No platform fees to convert");
            }
        } else {
            console.log("Already migrated to target version");
        }

        // Check public parameters
        console.log("\n=== Current Parameters ===");
        uint256 bondingCurveTradeCap = pool.bondingCurveTradeCap();
        uint256 minimumUsdtToBuy = pool.minimumUsdtToBuy();

        console.log("Bonding curve trade cap:", bondingCurveTradeCap);
        console.log("Minimum USDT to buy:", minimumUsdtToBuy);

        // Expected values after migration
        console.log("\n=== Expected Values After Migration ===");
        console.log("Expected version: 2025081901");
        console.log("Expected bonding curve trade cap: 400000000000000000000000000");
        console.log("Expected minimum USDT to buy: 1000");
    }

    function simulateMigration() public {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        vm.startBroadcast(deployerPrivateKey);

        console.log("=== Simulating Migration ===");

        // Record pre-migration state
        uint256 preNativeBalance = address(distributionPool).balance;
        uint256 preUsdtBalance = usdtToken.balanceOf(address(distributionPool));
        uint256 preTotalPlatformFee = distributionPool.totalPlatformFee();
        uint256 preVersion = distributionPool.currentVersion();

        console.log("Pre-migration state:");
        console.log("- Native balance:", preNativeBalance);
        console.log("- USDT balance:", preUsdtBalance);
        console.log("- Total platform fee:", preTotalPlatformFee);
        console.log("- Version:", preVersion);

        // Execute migration
        try distributionPool.migrateNewVersion() {
            console.log("Migration executed successfully");

            // Record post-migration state
            uint256 postNativeBalance = address(distributionPool).balance;
            uint256 postUsdtBalance = usdtToken.balanceOf(address(distributionPool));
            uint256 postTotalPlatformFee = distributionPool.totalPlatformFee();
            uint256 postVersion = distributionPool.currentVersion();

            console.log("Post-migration state:");
            console.log("- Native balance:", postNativeBalance);
            console.log("- USDT balance:", postUsdtBalance);
            console.log("- Total platform fee:", postTotalPlatformFee);
            console.log("- Version:", postVersion);

            // Verify changes
            console.log("Changes:");
            console.log("- Native balance change:", preNativeBalance - postNativeBalance);
            console.log("- USDT balance change:", postUsdtBalance - preUsdtBalance);
            console.log("- Version updated:", postVersion == 2_025_081_901);
        } catch Error(string memory reason) {
            console.log("Migration failed:", reason);
        }

        vm.stopBroadcast();
    }
}
