// // SPDX-License-Identifier: UNLICENSED
// pragma solidity ^0.8.13;

// import {Script, console} from "forge-std/Script.sol";
// import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
// import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
// import {Factory} from "../src/Factory.sol";
// import {DistributionPool} from "../src/DistributionPool.sol";
// import {IPCoin} from "../src/IPCoin.sol";

// /**
//  * @title ComprehensiveMigrationV3
//  * @notice 综合迁移脚本 - 升级合约并执行迁移流程
//  * @dev 按照以下顺序执行：
//  *      1. 升级 Factory 合约
//  *      2. 升级 DistributionPool 合约
//  *      3. 执行 migrateNewVersion() 全局迁移
//  *      4. 对现有币种执行 migrateCoinToUSDT() 迁移
//  */
// contract ComprehensiveMigrationV3 is Script {
//     // 环境变量配置
//     uint256 private deployerPrivateKey;
//     address private factoryProxyAddress;
//     address private poolProxyAddress;

//     // 合约实例
//     Factory private factory;
//     DistributionPool private distributionPool;

//     // 迁移状态跟踪
//     bool private factoryUpgraded = false;
//     bool private poolUpgraded = false;
//     bool private globalMigrated = false;

//     function setUp() public {
//         // 从环境变量读取配置
//         deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
//         factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
//         poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

//         // 初始化合约实例
//         factory = Factory(factoryProxyAddress);
//         distributionPool = DistributionPool(payable(poolProxyAddress));

//         console.log("=== Migration Script Configuration ===");
//         console.log("Factory Proxy Address:", factoryProxyAddress);
//         console.log("Pool Proxy Address:", poolProxyAddress);
//         console.log("Deployer Address:", vm.addr(deployerPrivateKey));
//     }

//     function run() public {
//         vm.startBroadcast(deployerPrivateKey);

//         console.log("=== Starting Comprehensive Migration Process ===");

//         // Step 1: Upgrade Factory contract
//         upgradeFactoryContract();

//         // Step 2: Upgrade DistributionPool contract
//         upgradeDistributionPoolContract();

//         // Step 3: Execute global migration
//         executeGlobalMigration();

//         // Step 4: Migrate existing coins
//         migrateCoinToUSDT();

//         console.log("=== Migration Process Completed ===");
//         _printMigrationSummary();

//         vm.stopBroadcast();
//     }

//     /**
//      * @notice Step 1: Upgrade Factory contract
//      */
//     function upgradeFactoryContract() internal {
//         console.log("=== Step 1: Upgrading Factory Contract ===");

//         try this.performFactoryUpgrade() {
//             factoryUpgraded = true;
//             console.log("Factory contract upgrade successful");
//         } catch Error(string memory reason) {
//             console.log("Factory contract upgrade failed:", reason);
//             // Continue execution, contract might already be latest version
//         } catch {
//             console.log("Factory contract upgrade failed: unknown error");
//         }
//     }

//     /**
//      * @notice Execute Factory upgrade logic
//      */
//     function performFactoryUpgrade() external {
//         // Deploy new Factory implementation contract
//         bytes32 factorySalt = keccak256(abi.encodePacked("Factory_Implementation_", block.timestamp));
//         Factory newFactoryImplementation = new Factory{salt: factorySalt}();
//         console.log("New Factory implementation address:", address(newFactoryImplementation));

//         // Upgrade proxy contract
//         UUPSUpgradeable factoryProxyContract = UUPSUpgradeable(factoryProxyAddress);
//         factoryProxyContract.upgradeToAndCall(address(newFactoryImplementation), "");

//         console.log("Factory proxy contract upgrade completed");
//     }

//     /**
//      * @notice 步骤 2: 升级 DistributionPool 合约
//      */
//     function upgradeDistributionPoolContract() internal {
//         console.log("=== 步骤 2: 升级 DistributionPool 合约 ===");

//         try this.performPoolUpgrade() {
//             poolUpgraded = true;
//             console.log("✅ DistributionPool 合约升级成功");
//         } catch Error(string memory reason) {
//             console.log("❌ DistributionPool 合约升级失败:", reason);
//             // 继续执行，可能合约已经是最新版本
//         } catch {
//             console.log("❌ DistributionPool 合约升级失败: 未知错误");
//         }
//     }

//     /**
//      * @notice 执行 DistributionPool 升级的具体逻辑
//      */
//     function performPoolUpgrade() external {
//         // 部署新的 DistributionPool 实现合约
//         bytes32 poolSalt = keccak256(abi.encodePacked("DistributionPool_Implementation_", block.timestamp));
//         DistributionPool newPoolImplementation = new DistributionPool{salt: poolSalt}();
//         console.log("新 DistributionPool 实现合约地址:", address(newPoolImplementation));

//         // 升级代理合约
//         UUPSUpgradeable poolProxyContract = UUPSUpgradeable(poolProxyAddress);
//         poolProxyContract.upgradeToAndCall(address(newPoolImplementation), "");

//         console.log("DistributionPool 代理合约升级完成");
//     }

//     /**
//      * @notice 步骤 3: 执行全局迁移
//      */
//     function executeGlobalMigration() internal {
//         console.log("=== 步骤 3: 执行全局迁移 ===");

//         // 记录迁移前状态
//         _recordPreMigrationState();

//         try distributionPool.migrateNewVersion() {
//             globalMigrated = true;
//             console.log("✅ 全局迁移成功");
//             _recordPostMigrationState();
//         } catch Error(string memory reason) {
//             console.log("❌ 全局迁移失败:", reason);
//             if (keccak256(bytes(reason)) == keccak256(bytes("Already migrated to new version"))) {
//                 console.log("ℹ️  合约已经迁移到新版本");
//                 globalMigrated = true;
//             }
//         } catch {
//             console.log("❌ 全局迁移失败: 未知错误");
//         }
//     }

//     /**
//      * @notice 记录迁移前状态
//      */
//     function _recordPreMigrationState() internal view {
//         console.log("\n--- 迁移前状态 ---");
//         console.log("当前版本:", distributionPool.currentVersion());
//         console.log("总平台费用:", distributionPool.totalPlatformFee());
//         console.log("合约原生代币余额:", address(distributionPool).balance);

//         // 尝试获取 USDT 余额（如果已设置）
//         try distributionPool.usdtToken() returns (address usdtAddress) {
//             if (usdtAddress != address(0)) {
//                 console.log("合约 USDT 余额:", distributionPool.usdtToken().balanceOf(address(distributionPool)));
//             }
//         } catch {
//             console.log("USDT 代币尚未设置");
//         }
//     }

//     /**
//      * @notice 记录迁移后状态
//      */
//     function _recordPostMigrationState() internal view {
//         console.log("\n--- 迁移后状态 ---");
//         console.log("当前版本:", distributionPool.currentVersion());
//         console.log("总平台费用:", distributionPool.totalPlatformFee());
//         console.log("合约原生代币余额:", address(distributionPool).balance);
//         console.log("合约 USDT 余额:", distributionPool.usdtToken().balanceOf(address(distributionPool)));
//     }

//     /**
//      * @notice 步骤 4: 迁移现有币种到 USDT
//      */
//     function migrateCoinToUSDT() internal {
//         console.log("=== 步骤 4: 迁移现有币种到 USDT ===");

//         // 这里需要根据实际情况添加要迁移的币种地址
//         // 可以从环境变量或配置文件中读取
//         address[] memory coinsToMigrate = _getCoinsToMigrate();

//         if (coinsToMigrate.length == 0) {
//             console.log("ℹ️  没有需要迁移的币种");
//             return;
//         }

//         console.log("需要迁移的币种数量:", coinsToMigrate.length);

//         for (uint256 i = 0; i < coinsToMigrate.length; i++) {
//             _migrateSingleCoin(coinsToMigrate[i]);
//         }
//     }

//     /**
//      * @notice 获取需要迁移的币种列表
//      * @dev 这里可以根据实际需求修改，比如从环境变量读取或查询链上数据
//      */
//     function _getCoinsToMigrate() internal view returns (address[] memory) {
//         // 尝试从多个环境变量读取币种地址
//         address[] memory tempCoins = new address[](10); // 最多支持10个币种
//         uint256 coinCount = 0;

//         // 尝试读取 COIN_TO_MIGRATE_1 到 COIN_TO_MIGRATE_10
//         for (uint256 i = 1; i <= 10; i++) {
//             string memory envVar = string(abi.encodePacked("COIN_TO_MIGRATE_", vm.toString(i)));
//             try vm.envAddress(envVar) returns (address coinAddress) {
//                 if (coinAddress != address(0)) {
//                     tempCoins[coinCount] = coinAddress;
//                     coinCount++;
//                     console.log("发现待迁移币种", i, ":", coinAddress);
//                 }
//             } catch {
//                 // 如果环境变量不存在，跳过
//                 break;
//             }
//         }

//         // 创建正确大小的数组
//         address[] memory coins = new address[](coinCount);
//         for (uint256 i = 0; i < coinCount; i++) {
//             coins[i] = tempCoins[i];
//         }

//         return coins;
//     }

//     /**
//      * @notice 验证合约升级是否成功
//      */
//     function verifyUpgrades() external view {
//         console.log("=== 验证合约升级状态 ===");

//         // 验证 Factory 合约
//         try factory.owner() returns (address owner) {
//             console.log("✅ Factory 合约可访问，所有者:", owner);
//         } catch {
//             console.log("❌ Factory 合约访问失败");
//         }

//         // 验证 DistributionPool 合约
//         try distributionPool.currentVersion() returns (uint256 version) {
//             console.log("✅ DistributionPool 合约可访问，版本:", version);
//         } catch {
//             console.log("❌ DistributionPool 合约访问失败");
//         }
//     }

//     /**
//      * @notice 紧急停止函数 - 如果发现问题可以调用
//      */
//     function emergencyStop() external {
//         console.log("🚨 紧急停止迁移流程");
//         revert("Migration stopped by emergency function");
//     }

//     /**
//      * @notice 迁移单个币种
//      */
//     function _migrateSingleCoin(
//         address coinAddress
//     ) internal {
//         console.log("\n--- 迁移币种:", coinAddress, "---");

//         // 检查币种是否已经迁移
//         try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
//             if (migrated) {
//                 console.log("ℹ️  币种已经迁移:", coinAddress);
//                 return;
//             }
//         } catch {
//             console.log("❌ 无法检查币种迁移状态:", coinAddress);
//             return;
//         }

//         // 执行币种迁移
//         uint256 deadline = block.timestamp + 300; // 5分钟后过期

//         try distributionPool.migrateCoinToUSDT(coinAddress, 0, deadline) {
//             console.log("✅ 币种迁移成功:", coinAddress);

//             // 验证迁移状态
//             bool migrated = distributionPool.isCoinMigratedToUSDT(coinAddress);
//             require(migrated, "币种迁移状态验证失败");
//             console.log("✅ 币种迁移验证通过:", coinAddress);
//         } catch Error(string memory reason) {
//             console.log("❌ 币种迁移失败:", coinAddress);
//             console.log("失败原因:", reason);
//         } catch {
//             console.log("❌ 币种迁移失败，未知错误:", coinAddress);
//         }
//     }

//     /**
//      * @notice 打印迁移总结
//      */
//     function _printMigrationSummary() internal view {
//         console.log("Factory 升级状态:", factoryUpgraded ? "✅ 成功" : "❌ 失败");
//         console.log("Pool 升级状态:", poolUpgraded ? "✅ 成功" : "❌ 失败");
//         console.log("全局迁移状态:", globalMigrated ? "✅ 成功" : "❌ 失败");

//         console.log("=== 迁移后最终状态 ===");
//         console.log("当前版本:", distributionPool.currentVersion());
//         console.log("合约原生代币余额:", address(distributionPool).balance);

//         try distributionPool.usdtToken() returns (address usdtAddress) {
//             if (usdtAddress != address(0)) {
//                 console.log("合约 USDT 余额:", distributionPool.usdtToken().balanceOf(address(distributionPool)));
//             }
//         } catch {
//             console.log("USDT 代币信息不可用");
//         }
//     }
// }
