# 合约迁移脚本说明

## 概述

本目录包含了一套完整的合约迁移脚本，用于按照现有的升级脚本和测试逻辑，依次执行 Factory 和 DistributionPool 合约的升级以及后续的迁移操作。

## 文件结构

```
script/
├── ComprehensiveMigrationV2.s.sol  # 主要迁移脚本
├── TestMigration.s.sol             # 测试脚本（只读检查）
├── run_migration.sh                # 便捷执行脚本
├── MIGRATION_USAGE.md              # 详细使用说明
└── README_MIGRATION.md             # 本文件
```

## 快速开始

### 1. 环境配置

在项目根目录的 `.env` 文件中设置以下变量：

```bash
# 必需变量
NETWORK_ADMIN_PRIVATE_KEY=0x...     # 具有管理员权限的私钥
FACTORY_PROXY_ADDRESS=0x...         # Factory 代理合约地址
POOL_PROXY_ADDRESS=0x...            # DistributionPool 代理合约地址
RPC_URL=https://...                 # RPC 节点地址

# 可选变量（用于币种迁移）
COIN_TO_MIGRATE_1=0x...             # 需要迁移的币种地址1
COIN_TO_MIGRATE_2=0x...             # 需要迁移的币种地址2

# API 密钥（用于合约验证）
BSCSCAN_API_KEY=...                 # BSC 网络
ETHERSCAN_API_KEY=...               # 以太坊网络
```

### 2. 权限检查

确保部署账户具有以下权限：
- Factory 合约的 Owner 权限
- DistributionPool 合约的 ADMIN_ROLE 权限

### 3. 执行迁移

#### 方法一：使用便捷脚本（推荐）

```bash
# 1. 运行测试检查当前状态
./script/run_migration.sh test

# 2. 运行模拟（不执行实际交易）
./script/run_migration.sh simulate

# 3. 执行实际迁移
./script/run_migration.sh migrate
```

#### 方法二：直接使用 forge

```bash
# 测试检查
forge script script/TestMigration.s.sol --rpc-url $RPC_URL

# 模拟迁移
forge script script/ComprehensiveMigrationV2.s.sol --rpc-url $RPC_URL

# 执行迁移
forge script script/ComprehensiveMigrationV2.s.sol --rpc-url $RPC_URL --broadcast --verify
```

## 迁移流程

脚本按以下顺序执行操作：

1. **权限检查** - 验证部署账户是否具有必要权限
2. **状态记录** - 记录迁移前的合约状态
3. **Factory 升级** - 部署新实现并升级 Factory 代理合约
4. **DistributionPool 升级** - 部署新实现并升级 DistributionPool 代理合约
5. **全局迁移** - 执行 `migrateNewVersion()` 更新系统参数
6. **币种迁移** - 对指定币种执行 `migrateCoinToUSDT()`
7. **结果验证** - 验证所有迁移操作是否成功完成

## 安全特性

- **幂等性** - 可以安全地多次运行
- **错误处理** - 对各种错误情况进行优雅处理
- **状态检查** - 跳过已完成的操作
- **详细日志** - 提供完整的执行日志用于调试

## 预期结果

成功执行后，系统将达到以下状态：

- **版本更新** - `currentVersion` 更新为 `2_025_081_901`
- **参数更新** - 债券曲线交易上限、最小 USDT 购买量等参数更新
- **费用迁移** - 平台费用从原生代币转换为 USDT
- **币种迁移** - 指定币种从原生代币迁移到 USDT

## 故障排除

### 常见问题

1. **权限不足**
   - 检查部署账户是否具有 Factory Owner 和 DistributionPool ADMIN_ROLE 权限

2. **已迁移错误**
   - 这是正常现象，脚本会跳过已完成的操作

3. **币种迁移失败**
   - 检查币种地址是否正确
   - 确认币种存在于 DistributionPool 中
   - 验证币种尚未迁移

4. **Gas 不足**
   - 确保部署账户有足够的原生代币支付 Gas 费用

### 手动验证

可以使用以下命令手动验证迁移结果：

```bash
# 检查当前版本
cast call $POOL_PROXY_ADDRESS "currentVersion()" --rpc-url $RPC_URL

# 检查债券曲线交易上限
cast call $POOL_PROXY_ADDRESS "bondingCurveTradeCap()" --rpc-url $RPC_URL

# 检查币种是否已迁移
cast call $POOL_PROXY_ADDRESS "isCoinMigratedToUSDT(address)" $COIN_ADDRESS --rpc-url $RPC_URL
```

## 网络配置示例

### BSC 主网
```bash
RPC_URL=https://bsc-dataseed1.binance.org/
BSCSCAN_API_KEY=your_bscscan_api_key
```

### BSC 测试网
```bash
RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BSCSCAN_API_KEY=your_bscscan_api_key
```

## 支持

如果遇到问题：

1. 查看控制台输出的详细错误信息
2. 验证所有环境变量设置正确
3. 确认部署账户权限和余额充足
4. 查看交易哈希进行链上验证

## 注意事项

- **备份重要数据** - 在执行迁移前备份重要的合约状态
- **测试环境验证** - 建议先在测试网络上验证脚本功能
- **监控执行过程** - 密切关注迁移过程中的日志输出
- **验证最终状态** - 迁移完成后验证所有参数和状态是否正确

---

*此迁移脚本基于现有的 `UpgradeToken.s.sol`、`UpgradeFactory.s.sol` 和 `MigrationTest.t.sol` 的逻辑设计，确保与现有系统的兼容性和一致性。*
