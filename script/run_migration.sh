#!/bin/bash

# Comprehensive Migration Script Runner
# This script provides a convenient way to run the migration scripts

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required environment variables are set
check_env_vars() {
    print_status "Checking required environment variables..."
    
    local required_vars=(
        "NETWORK_ADMIN_PRIVATE_KEY"
        "FACTORY_PROXY_ADDRESS" 
        "POOL_PROXY_ADDRESS"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "Please set these variables in your .env file or environment."
        exit 1
    fi
    
    print_success "All required environment variables are set"
}

# Function to check if .env file exists and load it
load_env() {
    if [[ -f .env ]]; then
        print_status "Loading environment variables from .env file..."
        export $(cat .env | grep -v '^#' | xargs)
        print_success "Environment variables loaded"
    else
        print_warning ".env file not found, using system environment variables"
    fi
}

# Function to display current configuration
show_config() {
    print_status "Current configuration:"
    echo "  Factory Proxy: ${FACTORY_PROXY_ADDRESS}"
    echo "  Pool Proxy: ${POOL_PROXY_ADDRESS}"
    echo "  RPC URL: ${RPC_URL:-'Not set'}"
    echo "  Network: ${NETWORK:-'Not specified'}"
    
    # Show optional coin addresses
    local coin_count=0
    for i in {1..10}; do
        local coin_var="COIN_TO_MIGRATE_$i"
        if [[ -n "${!coin_var}" ]]; then
            if [[ $coin_count -eq 0 ]]; then
                echo "  Coins to migrate:"
            fi
            echo "    $i: ${!coin_var}"
            ((coin_count++))
        fi
    done
    
    if [[ $coin_count -eq 0 ]]; then
        echo "  Coins to migrate: None specified"
    fi
    echo ""
}

# Function to run the test migration script
run_test() {
    print_status "Running migration test (read-only)..."
    
    if forge script script/TestMigration.s.sol --rpc-url "${RPC_URL}"; then
        print_success "Migration test completed successfully"
    else
        print_error "Migration test failed"
        exit 1
    fi
}

# Function to run the actual migration
run_migration() {
    local dry_run=${1:-false}
    
    if [[ "$dry_run" == "true" ]]; then
        print_status "Running migration simulation (dry run)..."
        local cmd="forge script script/ComprehensiveMigrationV2.s.sol --rpc-url \"${RPC_URL}\""
    else
        print_status "Running actual migration..."
        local cmd="forge script script/ComprehensiveMigrationV2.s.sol --rpc-url \"${RPC_URL}\" --broadcast"
        
        # Add verification if API key is available
        if [[ -n "${ETHERSCAN_API_KEY}" ]] || [[ -n "${BSCSCAN_API_KEY}" ]]; then
            cmd="$cmd --verify"
            if [[ -n "${BSCSCAN_API_KEY}" ]]; then
                cmd="$cmd --etherscan-api-key \"${BSCSCAN_API_KEY}\""
            elif [[ -n "${ETHERSCAN_API_KEY}" ]]; then
                cmd="$cmd --etherscan-api-key \"${ETHERSCAN_API_KEY}\""
            fi
        fi
    fi
    
    print_status "Executing: $cmd"
    
    if eval "$cmd"; then
        if [[ "$dry_run" == "true" ]]; then
            print_success "Migration simulation completed successfully"
        else
            print_success "Migration completed successfully"
        fi
    else
        if [[ "$dry_run" == "true" ]]; then
            print_error "Migration simulation failed"
        else
            print_error "Migration failed"
        fi
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  test        Run migration test (read-only check)"
    echo "  simulate    Run migration simulation (dry run)"
    echo "  migrate     Run actual migration"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --rpc-url URL    Override RPC URL"
    echo "  --network NAME   Set network name (for display only)"
    echo ""
    echo "Environment Variables:"
    echo "  Required:"
    echo "    NETWORK_ADMIN_PRIVATE_KEY  Private key with admin permissions"
    echo "    FACTORY_PROXY_ADDRESS      Factory proxy contract address"
    echo "    POOL_PROXY_ADDRESS         DistributionPool proxy contract address"
    echo "    RPC_URL                    RPC endpoint URL"
    echo ""
    echo "  Optional:"
    echo "    COIN_TO_MIGRATE_1          First coin address to migrate"
    echo "    COIN_TO_MIGRATE_2          Second coin address to migrate"
    echo "    BSCSCAN_API_KEY           BSC API key for verification"
    echo "    ETHERSCAN_API_KEY         Ethereum API key for verification"
    echo ""
    echo "Examples:"
    echo "  $0 test                     # Run migration test"
    echo "  $0 simulate                 # Run migration simulation"
    echo "  $0 migrate                  # Run actual migration"
    echo "  $0 migrate --rpc-url https://bsc-dataseed1.binance.org/"
}

# Main script logic
main() {
    local command=${1:-help}
    shift || true
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --rpc-url)
                RPC_URL="$2"
                shift 2
                ;;
            --network)
                NETWORK="$2"
                shift 2
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Load environment variables
    load_env
    
    # Check if RPC_URL is set
    if [[ -z "${RPC_URL}" ]]; then
        print_error "RPC_URL is not set"
        echo "Please set RPC_URL in your .env file or use --rpc-url option"
        exit 1
    fi
    
    case $command in
        test)
            check_env_vars
            show_config
            run_test
            ;;
        simulate)
            check_env_vars
            show_config
            print_warning "This is a simulation - no transactions will be broadcast"
            read -p "Continue? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                run_migration true
            else
                print_status "Simulation cancelled"
            fi
            ;;
        migrate)
            check_env_vars
            show_config
            print_warning "This will execute actual transactions on the blockchain"
            print_warning "Make sure you have reviewed the configuration above"
            read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                run_migration false
            else
                print_status "Migration cancelled"
            fi
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
