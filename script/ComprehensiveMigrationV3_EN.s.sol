// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IPCoin} from "../src/IPCoin.sol";

/**
 * @title ComprehensiveMigrationV3_EN
 * @notice Comprehensive migration script - upgrade contracts and execute migration process
 * @dev Execute in the following order:
 *      1. Upgrade Factory contract
 *      2. Upgrade DistributionPool contract
 *      3. Execute migrateNewVersion() global migration
 *      4. Execute migrateCoinToUSDT() for existing coins
 */
contract ComprehensiveMigrationV3_EN is Script {
    // Environment variable configuration
    uint256 private deployerPrivateKey;
    address private factoryProxyAddress;
    address private poolProxyAddress;
    address private currentAdmin; // Current admin address on chain

    // Contract instances
    Factory private factory;
    DistributionPool private distributionPool;

    // Migration status tracking
    bool private factoryUpgraded = false;
    bool private poolUpgraded = false;
    bool private globalMigrated = false;
    bool private adminRightsGranted = false;

    function setUp() public {
        // Read configuration from environment variables
        deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
        poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");
        currentAdmin = vm.envAddress("CURRENT_ADMIN_ADDRESS"); // Current admin on chain

        // Initialize contract instances
        factory = Factory(factoryProxyAddress);
        distributionPool = DistributionPool(payable(poolProxyAddress));

        console.log("=== Migration Script Configuration ===");
        console.log("Factory Proxy Address:", factoryProxyAddress);
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Current Admin Address:", currentAdmin);
        console.log("Deployer Address:", vm.addr(deployerPrivateKey));
    }

    function run() public {
        vm.startBroadcast(deployerPrivateKey);

        console.log("=== Starting Comprehensive Migration Process ===");

        // Step 0: Grant admin rights to deployer using mock admin
        grantAdminRights();

        // Step 1: Upgrade Factory contract
        upgradeFactoryContract();

        // Step 2: Upgrade DistributionPool contract
        upgradeDistributionPoolContract();

        // Step 3: Execute global migration
        executeGlobalMigration();

        // Step 4: Migrate existing coins
        migrateCoinToUSDT();

        console.log("=== Migration Process Completed ===");
        _printMigrationSummary();

        vm.stopBroadcast();
    }

    /**
     * @notice Step 0: Grant admin rights to deployer using mock admin
     */
    function grantAdminRights() internal {
        console.log("=== Step 0: Granting Admin Rights to Deployer ===");

        address deployerAddress = vm.addr(deployerPrivateKey);

        // Check if deployer already has admin rights
        if (distributionPool.checkAdmin(deployerAddress)) {
            console.log("Deployer already has admin rights");
            adminRightsGranted = true;
            return;
        }

        // Verify current admin has admin rights
        require(distributionPool.checkAdmin(currentAdmin), "Current admin address does not have admin rights");

        console.log("Using mock admin to grant rights:", currentAdmin);
        console.log("Granting admin rights to:", deployerAddress);

        // Stop current broadcast to use prank
        vm.stopBroadcast();

        try this.performAdminGrant(deployerAddress) {
            adminRightsGranted = true;
            console.log("Successfully granted admin rights to deployer");

            // Verify the grant was successful
            require(distributionPool.checkAdmin(deployerAddress), "Failed to verify admin rights grant");
            console.log("Admin rights verification passed");

        } catch Error(string memory reason) {
            console.log("Failed to grant admin rights:", reason);
        } catch {
            console.log("Failed to grant admin rights: unknown error");
        }

        // Resume broadcast with deployer key
        vm.startBroadcast(deployerPrivateKey);
    }

    /**
     * @notice Execute admin grant logic
     */
    function performAdminGrant(address deployerAddress) external {
        // Use vm.prank to impersonate the current admin
        vm.prank(currentAdmin);
        distributionPool.addAdmin(deployerAddress);
    }

    /**
     * @notice Step 1: Upgrade Factory contract
     */
    function upgradeFactoryContract() internal {
        console.log("=== Step 1: Upgrading Factory Contract ===");

        try this.performFactoryUpgrade() {
            factoryUpgraded = true;
            console.log("Factory contract upgrade successful");
        } catch Error(string memory reason) {
            console.log("Factory contract upgrade failed:", reason);
            // Continue execution, contract might already be latest version
        } catch {
            console.log("Factory contract upgrade failed: unknown error");
        }
    }

    /**
     * @notice Execute Factory upgrade logic
     */
    function performFactoryUpgrade() external {
        // Deploy new Factory implementation contract
        bytes32 factorySalt = keccak256(abi.encodePacked("Factory_Implementation_", block.timestamp));
        Factory newFactoryImplementation = new Factory{salt: factorySalt}();
        console.log("New Factory implementation address:", address(newFactoryImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable factoryProxyContract = UUPSUpgradeable(factoryProxyAddress);
        factoryProxyContract.upgradeToAndCall(address(newFactoryImplementation), "");

        console.log("Factory proxy contract upgrade completed");
    }

    /**
     * @notice Step 2: Upgrade DistributionPool contract
     */
    function upgradeDistributionPoolContract() internal {
        console.log("=== Step 2: Upgrading DistributionPool Contract ===");

        try this.performPoolUpgrade() {
            poolUpgraded = true;
            console.log("DistributionPool contract upgrade successful");
        } catch Error(string memory reason) {
            console.log("DistributionPool contract upgrade failed:", reason);
            // Continue execution, contract might already be latest version
        } catch {
            console.log("DistributionPool contract upgrade failed: unknown error");
        }
    }

    /**
     * @notice Execute DistributionPool upgrade logic
     */
    function performPoolUpgrade() external {
        // Deploy new DistributionPool implementation contract
        bytes32 poolSalt = keccak256(abi.encodePacked("DistributionPool_Implementation_", block.timestamp));
        DistributionPool newPoolImplementation = new DistributionPool{salt: poolSalt}();
        console.log("New DistributionPool implementation address:", address(newPoolImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable poolProxyContract = UUPSUpgradeable(poolProxyAddress);
        poolProxyContract.upgradeToAndCall(address(newPoolImplementation), "");

        console.log("DistributionPool proxy contract upgrade completed");
    }

    /**
     * @notice Step 3: Execute global migration
     */
    function executeGlobalMigration() internal {
        console.log("=== Step 3: Executing Global Migration ===");

        // Record pre-migration state
        _recordPreMigrationState();

        try distributionPool.migrateNewVersion() {
            globalMigrated = true;
            console.log("Global migration successful");
            _recordPostMigrationState();
        } catch Error(string memory reason) {
            console.log("Global migration failed:", reason);
            if (keccak256(bytes(reason)) == keccak256(bytes("Already migrated to new version"))) {
                console.log("Contract already migrated to new version");
                globalMigrated = true;
            }
        } catch {
            console.log("Global migration failed: unknown error");
        }
    }

    /**
     * @notice Record pre-migration state
     */
    function _recordPreMigrationState() internal view {
        console.log("\n--- Pre-Migration State ---");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Total platform fee:", distributionPool.totalPlatformFee());
        console.log("Contract native balance:", address(distributionPool).balance);

        // Try to get USDT balance (if already set)
        try distributionPool.usdtToken() returns (IERC20 usdtToken) {
            if (address(usdtToken) != address(0)) {
                console.log("Contract USDT balance:", usdtToken.balanceOf(address(distributionPool)));
            }
        } catch {
            console.log("USDT token not yet set");
        }
    }

    /**
     * @notice Record post-migration state
     */
    function _recordPostMigrationState() internal view {
        console.log("\n--- Post-Migration State ---");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Total platform fee:", distributionPool.totalPlatformFee());
        console.log("Contract native balance:", address(distributionPool).balance);
        console.log("Contract USDT balance:", distributionPool.usdtToken().balanceOf(address(distributionPool)));
    }

    /**
     * @notice Step 4: Migrate existing coins to USDT
     */
    function migrateCoinToUSDT() internal {
        console.log("=== Step 4: Migrating Existing Coins to USDT ===");

        // Get coins to migrate based on actual requirements
        // Can be read from environment variables or configuration files
        address[] memory coinsToMigrate = _getCoinsToMigrate();

        if (coinsToMigrate.length == 0) {
            console.log("No coins to migrate");
            return;
        }

        console.log("Number of coins to migrate:", coinsToMigrate.length);

        for (uint256 i = 0; i < coinsToMigrate.length; i++) {
            _migrateSingleCoin(coinsToMigrate[i]);
        }
    }

    /**
     * @notice Get list of coins to migrate
     * @dev Can be modified based on actual requirements, e.g., read from environment variables or query on-chain data
     */
    function _getCoinsToMigrate() internal view returns (address[] memory) {
        // Try to read coin addresses from multiple environment variables
        address[] memory tempCoins = new address[](10); // Support up to 10 coins
        uint256 coinCount = 0;

        // Try to read COIN_TO_MIGRATE_1 to COIN_TO_MIGRATE_10
        for (uint256 i = 1; i <= 10; i++) {
            string memory envVar = string(abi.encodePacked("COIN_TO_MIGRATE_", vm.toString(i)));
            try vm.envAddress(envVar) returns (address coinAddress) {
                if (coinAddress != address(0)) {
                    tempCoins[coinCount] = coinAddress;
                    coinCount++;
                    console.log("Found coin to migrate", i, ":", coinAddress);
                }
            } catch {
                // If environment variable doesn't exist, skip
                break;
            }
        }

        // Create correctly sized array
        address[] memory coins = new address[](coinCount);
        for (uint256 i = 0; i < coinCount; i++) {
            coins[i] = tempCoins[i];
        }

        return coins;
    }

    /**
     * @notice Migrate single coin
     */
    function _migrateSingleCoin(address coinAddress) internal {
        console.log("\n--- Migrating coin:", coinAddress, "---");

        // Check if coin is already migrated
        try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
            if (migrated) {
                console.log("Coin already migrated:", coinAddress);
                return;
            }
        } catch {
            console.log("Cannot check coin migration status:", coinAddress);
            return;
        }

        // Execute coin migration
        uint256 deadline = block.timestamp + 300; // Expires in 5 minutes

        try distributionPool.migrateCoinToUSDT(coinAddress, 0, deadline) {
            console.log("Coin migration successful:", coinAddress);

            // Verify migration status
            bool migrated = distributionPool.isCoinMigratedToUSDT(coinAddress);
            require(migrated, "Coin migration status verification failed");
            console.log("Coin migration verification passed:", coinAddress);

        } catch Error(string memory reason) {
            console.log("Coin migration failed:", coinAddress);
            console.log("Failure reason:", reason);
        } catch {
            console.log("Coin migration failed with unknown error:", coinAddress);
        }
    }

    /**
     * @notice Print migration summary
     */
    function _printMigrationSummary() internal view {
        console.log("Admin rights grant status:", adminRightsGranted ? "Success" : "Failed");
        console.log("Factory upgrade status:", factoryUpgraded ? "Success" : "Failed");
        console.log("Pool upgrade status:", poolUpgraded ? "Success" : "Failed");
        console.log("Global migration status:", globalMigrated ? "Success" : "Failed");

        console.log("=== Final State After Migration ===");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Contract native balance:", address(distributionPool).balance);

        try distributionPool.usdtToken() returns (IERC20 usdtToken) {
            if (address(usdtToken) != address(0)) {
                console.log("Contract USDT balance:", usdtToken.balanceOf(address(distributionPool)));
            }
        } catch {
            console.log("USDT token information unavailable");
        }
    }
}
