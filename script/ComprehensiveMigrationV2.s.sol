// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IPCoin} from "../src/IPCoin.sol";

/**
 * @title ComprehensiveMigrationV2
 * @notice Comprehensive migration script that executes contract upgrades and migrations in sequence
 * @dev Execution order:
 *      1. Upgrade Factory contract
 *      2. Upgrade DistributionPool contract
 *      3. Execute global migration (migrateNewVersion)
 *      4. Execute coin migrations (migrateCoinToUSDT)
 */
contract ComprehensiveMigrationV2 is Script {
    // Environment variables
    uint256 private deployerPrivateKey;
    address private factoryProxyAddress;
    address private poolProxyAddress;
    
    // Contract instances
    Factory private factory;
    DistributionPool private distributionPool;

    function setUp() public {
        // Read configuration from environment variables
        deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
        poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");
        
        // Initialize contract instances
        factory = Factory(factoryProxyAddress);
        distributionPool = DistributionPool(payable(poolProxyAddress));
    }

    function run() public {
        console.log("=== Starting Comprehensive Migration Process ===");
        console.log("Factory Proxy Address:", factoryProxyAddress);
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Deployer Address:", vm.addr(deployerPrivateKey));

        // Pre-check: Verify permissions and record initial state
        checkUpgradePermissions();
        recordPreMigrationState();

        vm.startBroadcast(deployerPrivateKey);

        // Step 1: Upgrade Factory contract
        upgradeFactoryContract();

        // Step 2: Upgrade DistributionPool contract
        upgradeDistributionPoolContract();

        // Step 3: Execute global migration
        executeGlobalMigration();

        // Step 4: Execute coin migrations (if there are coins to migrate)
        executeCoinMigrations();

        // Step 5: Verify migration results
        verifyMigrationResults();

        vm.stopBroadcast();

        // Post-processing: Display migration summary
        getMigrationSummary();

        console.log("=== Comprehensive Migration Process Completed ===");
    }

    /**
     * @notice Step 1: Upgrade Factory contract
     */
    function upgradeFactoryContract() internal {
        console.log("\n=== Step 1: Upgrading Factory Contract ===");

        // Deploy new Factory implementation contract
        bytes32 factorySalt = keccak256(abi.encodePacked("Factory_Implementation_", block.timestamp));
        Factory newFactoryImplementation = new Factory{salt: factorySalt}();
        console.log("New Factory implementation deployed at:", address(newFactoryImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable factoryProxyContract = UUPSUpgradeable(factoryProxyAddress);
        factoryProxyContract.upgradeToAndCall(address(newFactoryImplementation), "");

        console.log("Factory proxy contract upgrade completed");
        
        // Verify upgrade
        console.log("Factory admin:", factory.admin());
        console.log("Factory distributionPool:", address(factory.distributionPool()));
    }

    /**
     * @notice Step 2: Upgrade DistributionPool contract
     */
    function upgradeDistributionPoolContract() internal {
        console.log("\n=== Step 2: Upgrading DistributionPool Contract ===");

        // Deploy new DistributionPool implementation contract
        bytes32 poolSalt = keccak256(abi.encodePacked("DistributionPool_Implementation_", block.timestamp));
        DistributionPool newPoolImplementation = new DistributionPool{salt: poolSalt}();
        console.log("New DistributionPool implementation deployed at:", address(newPoolImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable poolProxyContract = UUPSUpgradeable(poolProxyAddress);
        poolProxyContract.upgradeToAndCall(address(newPoolImplementation), "");

        console.log("DistributionPool proxy contract upgrade completed");
        
        // Verify upgrade
        console.log("Pool currentVersion:", distributionPool.currentVersion());
        console.log("Pool totalPlatformFee:", distributionPool.totalPlatformFee());
    }

    /**
     * @notice Step 3: Execute global migration
     */
    function executeGlobalMigration() internal {
        console.log("\n=== Step 3: Executing Global Migration ===");

        // Record pre-migration state
        uint256 preNativeBalance = address(distributionPool).balance;
        uint256 preTotalPlatformFee = distributionPool.totalPlatformFee();
        uint256 preVersion = distributionPool.currentVersion();

        console.log("Pre-migration state:");
        console.log("- Contract native token balance:", preNativeBalance);
        console.log("- Total platform fee:", preTotalPlatformFee);
        console.log("- Current version:", preVersion);

        // Check if already migrated to new version
        if (preVersion >= 2_025_081_901) {
            console.log("Warning: Already migrated to new version, skipping global migration");
            return;
        }

        // Execute global migration
        try distributionPool.migrateNewVersion() {
            console.log("Global migration executed successfully");
        } catch Error(string memory reason) {
            console.log("Global migration failed:", reason);
            return;
        }

        // Record post-migration state
        uint256 postNativeBalance = address(distributionPool).balance;
        uint256 postTotalPlatformFee = distributionPool.totalPlatformFee();
        uint256 postVersion = distributionPool.currentVersion();

        console.log("Post-migration state:");
        console.log("- Contract native token balance:", postNativeBalance);
        console.log("- Total platform fee:", postTotalPlatformFee);
        console.log("- Current version:", postVersion);

        // Verify migration results
        require(postVersion == 2_025_081_901, "Version update failed");
        console.log("Global migration verification passed");
    }

    /**
     * @notice Step 4: Execute coin migrations
     */
    function executeCoinMigrations() internal {
        console.log("\n=== Step 4: Executing Coin Migrations ===");

        // Add coin addresses that need to be migrated based on actual situation
        // Since test environment may not have actual coins, this provides a framework
        
        // Example: If there are environment variables specifying coins to migrate
        try vm.envAddress("COIN_TO_MIGRATE_1") returns (address coinAddress) {
            migrateSingleCoin(coinAddress);
        } catch {
            console.log("No coin address found for COIN_TO_MIGRATE_1");
        }

        // Can add more coin migrations
        try vm.envAddress("COIN_TO_MIGRATE_2") returns (address coinAddress2) {
            migrateSingleCoin(coinAddress2);
        } catch {
            console.log("No coin address found for COIN_TO_MIGRATE_2");
        }

        console.log("Coin migration phase completed");
    }

    /**
     * @notice Migrate single coin
     */
    function migrateSingleCoin(address coinAddress) internal {
        console.log("Starting coin migration:", coinAddress);

        // Check if coin is already migrated
        bool alreadyMigrated = distributionPool.isCoinMigratedToUSDT(coinAddress);
        if (alreadyMigrated) {
            console.log("Coin already migrated, skipping:", coinAddress);
            return;
        }

        // Try to migrate the coin directly
        uint256 deadline = block.timestamp + 300; // Expires in 5 minutes
        
        try distributionPool.migrateCoinToUSDT(coinAddress, 0, deadline) {
            console.log("Coin migration successful:", coinAddress);
            
            // Verify migration status
            bool migrated = distributionPool.isCoinMigratedToUSDT(coinAddress);
            require(migrated, "Coin migration status verification failed");
            console.log("Coin migration verification passed:", coinAddress);
            
        } catch Error(string memory reason) {
            console.log("Coin migration failed:", coinAddress);
            console.log("Failure reason:", reason);
        } catch {
            console.log("Coin migration failed with unknown error:", coinAddress);
        }
    }

    /**
     * @notice Step 5: Verify migration results
     */
    function verifyMigrationResults() internal {
        console.log("\n=== Step 5: Verifying Migration Results ===");

        // Verify version update
        uint256 currentVersion = distributionPool.currentVersion();
        console.log("Current version:", currentVersion);
        require(currentVersion == 2_025_081_901, "Version verification failed");

        // Verify parameter updates
        uint256 bondingCurveTradeCap = distributionPool.bondingCurveTradeCap();
        uint256 minimumUsdtToBuy = distributionPool.minimumUsdtToBuy();

        console.log("Bonding curve trade cap:", bondingCurveTradeCap);
        console.log("Minimum USDT to buy:", minimumUsdtToBuy);

        require(bondingCurveTradeCap == 400_000_000 * 1e18, "Bonding curve trade cap verification failed");
        require(minimumUsdtToBuy == 1 * 1e3, "Minimum USDT to buy verification failed");

        console.log("All migration results verification passed");
    }

    /**
     * @notice Helper function: Check contract upgrade permissions
     */
    function checkUpgradePermissions() internal view {
        console.log("\n=== Checking Upgrade Permissions ===");

        address deployer = vm.addr(deployerPrivateKey);

        // Check Factory upgrade permissions
        try factory.owner() returns (address factoryOwner) {
            console.log("Factory owner:", factoryOwner);
            console.log("Deployer address:", deployer);
            require(factoryOwner == deployer, "No Factory upgrade permission");
        } catch {
            console.log("Unable to get Factory owner information");
        }

        // Check DistributionPool admin permissions
        bytes32 adminRole = distributionPool.ADMIN_ROLE();
        bool hasAdminRole = distributionPool.hasRole(adminRole, deployer);
        console.log("Has DistributionPool admin permission:", hasAdminRole);
        require(hasAdminRole, "No DistributionPool admin permission");

        console.log("Permission check passed");
    }

    /**
     * @notice Helper function: Record complete pre-migration state
     */
    function recordPreMigrationState() internal view {
        console.log("\n=== Recording Pre-Migration State ===");

        // Record basic contract information
        console.log("Factory address:", factoryProxyAddress);
        console.log("DistributionPool address:", poolProxyAddress);

        // Record version information
        uint256 currentVersion = distributionPool.currentVersion();
        console.log("Current version:", currentVersion);

        // Record balance information
        uint256 nativeBalance = address(distributionPool).balance;
        uint256 totalPlatformFee = distributionPool.totalPlatformFee();
        console.log("Contract native token balance:", nativeBalance);
        console.log("Total platform fee:", totalPlatformFee);

        // Record parameter information
        uint256 bondingCurveTradeCap = distributionPool.bondingCurveTradeCap();
        uint256 minimumUsdtToBuy = distributionPool.minimumUsdtToBuy();
        console.log("Current bonding curve trade cap:", bondingCurveTradeCap);
        console.log("Current minimum USDT to buy:", minimumUsdtToBuy);
    }

    /**
     * @notice Get migration summary
     */
    function getMigrationSummary() internal view {
        console.log("\n=== Migration Summary ===");
        console.log("Factory proxy address:", factoryProxyAddress);
        console.log("DistributionPool proxy address:", poolProxyAddress);
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Total platform fee:", distributionPool.totalPlatformFee());
        console.log("Bonding curve trade cap:", distributionPool.bondingCurveTradeCap());
        console.log("Minimum USDT to buy:", distributionPool.minimumUsdtToBuy());
    }
}
