# Comprehensive Migration Script Usage Guide

## Overview

The `ComprehensiveMigrationV2.s.sol` script is designed to perform a complete migration of the contract system, following the logic from the existing upgrade scripts and test files. It executes the following operations in sequence:

1. **Factory Contract Upgrade** - Updates the Factory contract to the latest implementation
2. **DistributionPool Contract Upgrade** - Updates the DistributionPool contract to the latest implementation  
3. **Global Migration** - Executes `migrateNewVersion()` to update system parameters and migrate platform fees
4. **Coin Migrations** - Executes `migrateCoinToUSDT()` for individual coins that need migration
5. **Verification** - Validates that all migrations completed successfully

## Prerequisites

### Environment Variables

Before running the script, ensure the following environment variables are set in your `.env` file:

```bash
# Required variables
NETWORK_ADMIN_PRIVATE_KEY=0x...           # Private key with admin permissions
FACTORY_PROXY_ADDRESS=0x...               # Address of the Factory proxy contract
POOL_PROXY_ADDRESS=0x...                  # Address of the DistributionPool proxy contract

# Optional variables for coin migration
COIN_TO_MIGRATE_1=0x...                   # Address of first coin to migrate
COIN_TO_MIGRATE_2=0x...                   # Address of second coin to migrate
# Add more COIN_TO_MIGRATE_N as needed
```

### Permissions Required

The deployer account must have:
- **Factory Owner** permissions (to upgrade Factory contract)
- **DistributionPool ADMIN_ROLE** permissions (to upgrade DistributionPool and execute migrations)

## Usage

### Basic Execution

```bash
# Run the comprehensive migration script
forge script script/ComprehensiveMigrationV2.s.sol --rpc-url $RPC_URL --broadcast --verify
```

### Dry Run (Simulation)

To test the script without actually executing transactions:

```bash
# Simulate the migration without broadcasting
forge script script/ComprehensiveMigrationV2.s.sol --rpc-url $RPC_URL
```

### Network-Specific Examples

#### BSC Mainnet
```bash
forge script script/ComprehensiveMigrationV2.s.sol \
  --rpc-url $BSC_MAINNET_RPC \
  --broadcast \
  --verify \
  --etherscan-api-key $BSCSCAN_API_KEY
```

#### BSC Testnet
```bash
forge script script/ComprehensiveMigrationV2.s.sol \
  --rpc-url $BSC_TESTNET_RPC \
  --broadcast \
  --verify \
  --etherscan-api-key $BSCSCAN_API_KEY
```

## Script Behavior

### Step-by-Step Process

1. **Pre-checks**
   - Verifies deployer has necessary permissions
   - Records current state of contracts
   - Displays configuration information

2. **Factory Upgrade**
   - Deploys new Factory implementation
   - Upgrades Factory proxy to new implementation
   - Verifies upgrade success

3. **DistributionPool Upgrade**
   - Deploys new DistributionPool implementation
   - Upgrades DistributionPool proxy to new implementation
   - Verifies upgrade success

4. **Global Migration**
   - Checks if already migrated (version >= 2_025_081_901)
   - Executes `migrateNewVersion()` if needed
   - Swaps native tokens to USDT
   - Updates system parameters
   - Verifies version update

5. **Coin Migrations**
   - Attempts to migrate coins specified in environment variables
   - Skips coins that are already migrated
   - Handles errors gracefully for non-existent coins

6. **Verification**
   - Confirms version is updated to 2_025_081_901
   - Validates parameter updates (bonding curve cap, minimum USDT)
   - Displays final state

### Error Handling

The script includes comprehensive error handling:
- **Permission Errors**: Script will fail early if deployer lacks required permissions
- **Already Migrated**: Skips operations that have already been completed
- **Coin Migration Errors**: Logs errors but continues with other coins
- **Transaction Failures**: Provides detailed error messages

### Safety Features

- **Idempotent**: Can be run multiple times safely
- **Pre-checks**: Validates permissions before starting
- **Graceful Failures**: Continues execution even if individual coin migrations fail
- **Detailed Logging**: Provides comprehensive output for debugging

## Expected Output

### Successful Execution
```
=== Starting Comprehensive Migration Process ===
Factory Proxy Address: 0x...
Pool Proxy Address: 0x...
Deployer Address: 0x...

=== Checking Upgrade Permissions ===
Factory owner: 0x...
Deployer address: 0x...
Has DistributionPool admin permission: true
Permission check passed

=== Recording Pre-Migration State ===
Factory address: 0x...
DistributionPool address: 0x...
Current version: 0
...

=== Step 1: Upgrading Factory Contract ===
New Factory implementation deployed at: 0x...
Factory proxy contract upgrade completed
...

=== Step 5: Verifying Migration Results ===
Current version: **********
All migration results verification passed

=== Migration Summary ===
...

=== Comprehensive Migration Process Completed ===
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure the deployer account has Factory owner and DistributionPool admin permissions
   - Check that the private key corresponds to the correct account

2. **Already Migrated Error**
   - This is expected if the migration has already been run
   - The script will skip already completed operations

3. **Coin Migration Failures**
   - Check that coin addresses in environment variables are correct
   - Ensure coins exist in the DistributionPool
   - Verify coins haven't already been migrated

4. **Gas Issues**
   - Ensure sufficient native token balance for gas fees
   - Consider increasing gas limit if transactions fail

### Manual Verification

After running the script, you can manually verify the results:

```bash
# Check current version
cast call $POOL_PROXY_ADDRESS "currentVersion()" --rpc-url $RPC_URL

# Check bonding curve trade cap
cast call $POOL_PROXY_ADDRESS "bondingCurveTradeCap()" --rpc-url $RPC_URL

# Check if a coin is migrated
cast call $POOL_PROXY_ADDRESS "isCoinMigratedToUSDT(address)" $COIN_ADDRESS --rpc-url $RPC_URL
```

## Support

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure the deployer account has sufficient permissions and balance
4. Review the transaction hashes in the output for on-chain verification
