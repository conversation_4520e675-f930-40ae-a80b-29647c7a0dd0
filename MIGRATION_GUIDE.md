# DistributionPool 迁移指南

## 概述

本指南提供了 DistributionPool 合约从原生代币（ETH/BNB）迁移到 USDT 的完整测试和执行方案。

## 迁移功能说明

### 1. `migrateNewVersion()` - 全局迁移

- **功能**: 将整个合约升级到新版本 `2_025_081_901`
- **操作**:
  - 更新全局参数
  - 将 `totalPlatformFee` 中的原生代币通过 Uniswap 兑换为 USDT
  - 更新版本号

### 2. `migrateCoinToUSDT()` - 单币种迁移

- **功能**: 将特定币种从原生代币迁移到 USDT
- **操作**:
  - 将币种的原生代币储备兑换为 USDT
  - 更新币种参数到新版本
  - 标记币种为已迁移状态

## 测试方案

### 环境准备

1. **配置代理** (如果需要):

```bash
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897
```

2. **更新配置**:
   - 在测试文件中更新 `DISTRIBUTION_POOL_ADDRESS` 为实际部署的合约地址
   - 确认 USDT 代币地址正确

### 测试步骤

#### 1. 运行本地 Fork 测试

```bash
# 运行完整测试套件
forge test --match-contract MigrationTest --fork-url https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd -vvv

# 运行特定测试
forge test --match-test testMigrateNewVersion --fork-url https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd -vvv
```

#### 2. 检查当前状态

```bash
# 检查合约当前状态和迁移准备情况
forge script script/SimpleMigrationTest.s.sol --fork-url https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd -vvv
```

#### 3. 执行迁移模拟

```bash
# 模拟执行迁移（安全测试）
forge script script/SimpleMigrationTest.s.sol:SimpleMigrationTest --sig "simulateMigration()" --fork-url https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd -vvv

# 实际执行（谨慎使用）
forge script script/SimpleMigrationTest.s.sol:SimpleMigrationTest --sig "simulateMigration()" --fork-url https://proud-omniscient-thunder.base-sepolia.quiknode.pro/60735776dd52a92f6e939b93cda976e8d0426ddd --broadcast --private-key $PRIVATE_KEY
```

## 关键验证点

### 迁移前检查

- [ ] 合约版本 < `2_025_081_901`
- [ ] 合约原生代币余额 >= `totalPlatformFee`
- [ ] USDT 代币合约可访问
- [ ] Uniswap 路由器可用且有足够流动性

### 迁移后验证

- [ ] 合约版本 = `2_025_081_901`
- [ ] `totalPlatformFee` 已转换为 USDT 数量
- [ ] 合约 USDT 余额增加
- [ ] 合约原生代币余额减少（如果有转换）
- [ ] 全局参数正确更新
- [ ] 币种迁移状态正确标记

### 资产安全检查

- [ ] 转换汇率合理（无异常滑点）
- [ ] 总资产价值保持一致
- [ ] 无资产丢失或锁定

## 风险提示

### 高风险操作

1. **流动性风险**: 大额兑换可能导致严重滑点
2. **时间风险**: deadline 设置过短可能导致交易失败
3. **重入风险**: 虽然有保护，但仍需谨慎
4. **权限风险**: 确保只有授权地址可以执行迁移

### 建议措施

1. **分批迁移**: 对于大额资产，考虑分批进行
2. **滑点保护**: 设置合理的 `minUsdtOut` 参数
3. **时间缓冲**: 设置足够的 deadline 时间
4. **监控机制**: 实时监控迁移过程和结果

## 应急预案

### 迁移失败处理

1. **检查失败原因**: 查看交易日志和错误信息
2. **流动性问题**: 等待流动性恢复或调整参数
3. **技术问题**: 联系技术团队进行故障排除

### 回滚策略

- 注意：迁移是不可逆的，无法直接回滚
- 如果发现问题，需要通过新的迁移或修复操作来解决

## 执行清单

### 迁移前

- [ ] 完成所有测试
- [ ] 确认流动性充足
- [ ] 备份重要数据
- [ ] 通知相关方
- [ ] 准备监控工具

### 迁移中

- [ ] 实时监控交易状态
- [ ] 记录关键数据
- [ ] 准备应急响应

### 迁移后

- [ ] 验证所有检查点
- [ ] 测试核心功能
- [ ] 更新文档和配置
- [ ] 通知迁移完成

## 联系信息

如有问题或需要支持，请联系开发团队。

---

**重要提醒**: 在主网执行迁移前，请务必在测试网络上完整测试所有流程，并确保理解所有风险。
