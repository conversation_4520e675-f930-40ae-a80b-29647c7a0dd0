# Mock Admin Configuration Example
# Copy this file to .env and modify the values according to your setup

# Enable mock admin mode for testing
USE_MOCK_ADMIN=true

# Real admin address that will be impersonated (optional)
# If not provided, the script will try to get it from the contract owner
REAL_ADMIN_ADDRESS=0x1234567890123456789012345678901234567890

# Contract addresses (required)
FACTORY_PROXY_ADDRESS=0xYourFactoryProxyAddress
POOL_PROXY_ADDRESS=0xYourPoolProxyAddress

# Fallback private key (can be any valid private key, not used in mock mode)
NETWORK_ADMIN_PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80

# Optional: Coins to migrate (up to 10)
COIN_TO_MIGRATE_1=0xCoinAddress1
COIN_TO_MIGRATE_2=0xCoinAddress2
# COIN_TO_MIGRATE_3=0xCoinAddress3
# ... up to COIN_TO_MIGRATE_10

# Network RPC URL for forking
RPC_URL=https://your-rpc-endpoint.com